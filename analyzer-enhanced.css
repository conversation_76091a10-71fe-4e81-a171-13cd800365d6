/* CubeAI Solutions - Enhanced Resume Analyzer Styles */

/* Upload Area Enhancements */
.upload-area {
    border: 3px dashed #4a90e2;
    border-radius: 15px;
    padding: 3rem;
    text-align: center;
    background: linear-gradient(135deg, #f8f9ff 0%, #e8f2ff 100%);
    transition: all 0.3s ease;
    cursor: pointer;
    position: relative;
    overflow: hidden;
    min-height: 200px;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
}

.upload-area:hover {
    border-color: #357abd;
    background: linear-gradient(135deg, #e8f2ff 0%, #d4e9ff 100%);
    transform: translateY(-3px);
    box-shadow: 0 10px 30px rgba(74, 144, 226, 0.2);
}

.upload-area.drag-over {
    border-color: #28a745;
    background: linear-gradient(135deg, #f0fff4 0%, #e6ffed 100%);
    transform: scale(1.02);
    box-shadow: 0 15px 35px rgba(40, 167, 69, 0.3);
}

.upload-icon {
    font-size: 4.5rem;
    color: #4a90e2;
    margin-bottom: 1.5rem;
    animation: float 3s ease-in-out infinite;
}

@keyframes float {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-12px); }
}

.upload-area h3 {
    color: #2c3e50;
    margin-bottom: 0.8rem;
    font-weight: 700;
    font-size: 1.4rem;
}

.upload-area p {
    color: #6c757d;
    margin-bottom: 2rem;
    font-size: 1rem;
    line-height: 1.5;
}

.file-input {
    display: none;
}

/* File List Styling */
.file-item {
    background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
    border: 1px solid #e9ecef;
    border-radius: 10px;
    padding: 1rem;
    margin-bottom: 0.5rem;
    display: flex;
    align-items: center;
    transition: all 0.3s ease;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.file-item:hover {
    transform: translateX(5px);
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.file-item i {
    font-size: 1.5rem;
    margin-right: 1rem;
}

/* Enhanced Card Styling */
.card {
    border: none;
    border-radius: 15px;
    box-shadow: 0 5px 20px rgba(0, 0, 0, 0.08);
    transition: all 0.3s ease;
    overflow: hidden;
}

.card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.15);
}

.card-header {
    border-bottom: none;
    font-weight: 600;
    padding: 1.5rem;
}

.card-body {
    padding: 1.5rem;
}

/* Progress Bars */
.progress {
    height: 8px;
    border-radius: 10px;
    background-color: #e9ecef;
    overflow: hidden;
}

.progress-bar {
    border-radius: 10px;
    transition: width 1.5s ease-in-out;
}

/* Score Cards */
.score-card {
    text-align: center;
    padding: 2rem;
    border-radius: 15px;
    background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
    border: 1px solid #e9ecef;
    transition: all 0.3s ease;
}

.score-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
}

.score-card h3 {
    font-size: 3rem;
    font-weight: 700;
    margin-bottom: 0.5rem;
}

/* Alert Enhancements */
.alert {
    border: none;
    border-radius: 12px;
    padding: 1.25rem;
    margin-bottom: 1.5rem;
    box-shadow: 0 3px 15px rgba(0, 0, 0, 0.1);
}

.alert-success {
    background: linear-gradient(135deg, #d4edda 0%, #c3e6cb 100%);
    color: #155724;
    border-left: 4px solid #28a745;
}

.alert-warning {
    background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%);
    color: #856404;
    border-left: 4px solid #ffc107;
}

.alert-info {
    background: linear-gradient(135deg, #d1ecf1 0%, #bee5eb 100%);
    color: #0c5460;
    border-left: 4px solid #17a2b8;
}

.alert-danger {
    background: linear-gradient(135deg, #f8d7da 0%, #f5c6cb 100%);
    color: #721c24;
    border-left: 4px solid #dc3545;
}

/* Button Enhancements */
.btn {
    border-radius: 25px;
    font-weight: 600;
    padding: 0.75rem 1.5rem;
    transition: all 0.3s ease;
    border: none;
    position: relative;
    overflow: hidden;
}

.btn:before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s;
}

.btn:hover:before {
    left: 100%;
}

.btn-primary {
    background: linear-gradient(135deg, #4a90e2 0%, #357abd 100%);
    box-shadow: 0 4px 15px rgba(74, 144, 226, 0.3);
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(74, 144, 226, 0.4);
}

.btn-success {
    background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
    box-shadow: 0 4px 15px rgba(40, 167, 69, 0.3);
}

.btn-success:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(40, 167, 69, 0.4);
}

.btn-outline-primary {
    border: 2px solid #4a90e2;
    color: #4a90e2;
    background: transparent;
}

.btn-outline-primary:hover {
    background: #4a90e2;
    color: white;
    transform: translateY(-2px);
}

.btn-outline-success {
    border: 2px solid #28a745;
    color: #28a745;
    background: transparent;
}

.btn-outline-success:hover {
    background: #28a745;
    color: white;
    transform: translateY(-2px);
}

.btn-outline-danger {
    border: 2px solid #dc3545;
    color: #dc3545;
    background: transparent;
}

.btn-outline-danger:hover {
    background: #dc3545;
    color: white;
    transform: translateY(-2px);
}

/* Loading Spinner */
.spinner-border {
    width: 3rem;
    height: 3rem;
    border-width: 0.3em;
}

/* Skills Display */
.skill-badge {
    display: inline-block;
    background: linear-gradient(135deg, #4a90e2 0%, #357abd 100%);
    color: white;
    padding: 0.4rem 0.8rem;
    border-radius: 20px;
    font-size: 0.85rem;
    font-weight: 600;
    margin: 0.2rem;
    box-shadow: 0 2px 8px rgba(74, 144, 226, 0.3);
}

/* Suggestions List */
.suggestions-list {
    list-style: none;
    padding: 0;
}

.suggestions-list li {
    padding: 0.75rem 0;
    border-bottom: 1px solid #e9ecef;
    display: flex;
    align-items: center;
    transition: all 0.3s ease;
}

.suggestions-list li:hover {
    background: rgba(74, 144, 226, 0.05);
    padding-left: 1rem;
}

.suggestions-list li:last-child {
    border-bottom: none;
}

.suggestions-list i {
    color: #28a745;
    margin-right: 1rem;
    font-size: 1.1rem;
}

/* Animation Classes */
.fade-in {
    animation: fadeIn 0.5s ease-in;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}

.slide-up {
    animation: slideUp 0.6s ease-out;
}

@keyframes slideUp {
    from { opacity: 0; transform: translateY(30px); }
    to { opacity: 1; transform: translateY(0); }
}

/* Responsive Design */
@media (max-width: 768px) {
    .upload-area {
        padding: 2rem 1rem;
        min-height: 150px;
    }
    
    .upload-icon {
        font-size: 3rem;
    }
    
    .upload-area h3 {
        font-size: 1.2rem;
    }
    
    .card-body {
        padding: 1rem;
    }
    
    .btn {
        width: 100%;
        margin-bottom: 0.5rem;
    }
    
    .d-flex.gap-2 {
        flex-direction: column;
    }
}

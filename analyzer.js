/**
 * CubeAI Solutions - Advanced AI Resume Analyzer
 * Comprehensive Resume Parsing and Analysis Engine
 * Features: NLP Processing, Skill Extraction, Job Matching, ATS Scoring
 */

class CubeAIResumeAnalyzer {
    constructor() {
        this.uploadedFiles = [];
        this.analysisResults = [];
        this.jobDescription = '';
        this.skillsDatabase = this.initializeSkillsDatabase();
        this.industryKeywords = this.initializeIndustryKeywords();
        this.atsKeywords = this.initializeATSKeywords();
        this.init();
    }

    init() {
        console.log('🚀 Initializing CubeAI Resume Analyzer...');
        this.bindEvents();
        this.setupDragAndDrop();
        this.loadSampleJobDescription();
    }

    bindEvents() {
        // File input change
        const fileInput = document.getElementById('fileInput');
        if (fileInput) {
            fileInput.addEventListener('change', (e) => this.handleFileSelect(e));
        }

        // Job description change
        const jobDescInput = document.getElementById('jobDescription');
        if (jobDescInput) {
            jobDescInput.addEventListener('input', (e) => this.handleJobDescriptionChange(e));
        }

        // Upload area click
        const uploadArea = document.getElementById('uploadArea');
        if (uploadArea) {
            uploadArea.addEventListener('click', () => fileInput?.click());
            uploadArea.addEventListener('keypress', (e) => {
                if (e.key === 'Enter' || e.key === ' ') {
                    e.preventDefault();
                    fileInput?.click();
                }
            });
        }
    }

    setupDragAndDrop() {
        const uploadArea = document.getElementById('uploadArea');
        if (!uploadArea) return;

        ['dragenter', 'dragover', 'dragleave', 'drop'].forEach(eventName => {
            uploadArea.addEventListener(eventName, this.preventDefaults, false);
        });

        ['dragenter', 'dragover'].forEach(eventName => {
            uploadArea.addEventListener(eventName, () => uploadArea.classList.add('drag-over'), false);
        });

        ['dragleave', 'drop'].forEach(eventName => {
            uploadArea.addEventListener(eventName, () => uploadArea.classList.remove('drag-over'), false);
        });

        uploadArea.addEventListener('drop', (e) => this.handleDrop(e), false);
    }

    preventDefaults(e) {
        e.preventDefault();
        e.stopPropagation();
    }

    handleDrop(e) {
        const files = Array.from(e.dataTransfer.files);
        this.processFiles(files);
    }

    handleFileSelect(e) {
        const files = Array.from(e.target.files);
        this.processFiles(files);
    }

    handleJobDescriptionChange(e) {
        this.jobDescription = e.target.value.trim();
        console.log('📝 Job description updated');
    }

    async processFiles(files) {
        console.log(`📁 Processing ${files.length} file(s)...`);

        // Validate files
        const validFiles = this.validateFiles(files);
        if (validFiles.length === 0) return;

        // Show loading
        this.showLoading();

        // Update file display
        this.updateFileDisplay(validFiles);

        try {
            // Process each file
            for (const file of validFiles) {
                await this.analyzeResume(file);
            }

            // Show results
            this.showAnalysisResults();

        } catch (error) {
            console.error('Error processing files:', error);
            this.showError('Error processing files. Please try again.');
        } finally {
            this.hideLoading();
        }
    }

    validateFiles(files) {
        const validTypes = ['application/pdf', 'application/msword', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'];
        const maxSize = 5 * 1024 * 1024; // 5MB

        return files.filter(file => {
            if (!validTypes.includes(file.type)) {
                this.showError(`${file.name}: Unsupported file type. Please use PDF, DOC, or DOCX.`);
                return false;
            }

            if (file.size > maxSize) {
                this.showError(`${file.name}: File too large. Maximum size is 5MB.`);
                return false;
            }

            return true;
        });
    }

    async analyzeResume(file) {
        console.log(`🔍 Analyzing resume: ${file.name}`);

        try {
            // Extract text from file
            const resumeText = await this.extractTextFromFile(file);

            // Parse resume content
            const parsedData = this.parseResumeContent(resumeText);

            // Analyze skills and experience
            const skillsAnalysis = this.analyzeSkills(parsedData);

            // Calculate ATS score
            const atsScore = this.calculateATSScore(parsedData);

            // Job matching (if job description provided)
            const jobMatch = this.jobDescription ? this.calculateJobMatch(parsedData) : null;

            // Generate improvement suggestions
            const suggestions = this.generateSuggestions(parsedData, skillsAnalysis);

            // Store results
            const result = {
                fileName: file.name,
                fileSize: file.size,
                parsedData,
                skillsAnalysis,
                atsScore,
                jobMatch,
                suggestions,
                timestamp: new Date().toISOString()
            };

            this.analysisResults.push(result);

        } catch (error) {
            console.error(`Error analyzing ${file.name}:`, error);
            throw error;
        }
    }

    async extractTextFromFile(file) {
        // Simulate text extraction (in real implementation, use PDF.js or similar)
        return new Promise((resolve) => {
            const reader = new FileReader();
            reader.onload = (e) => {
                // For demo purposes, generate sample resume text
                const sampleText = this.generateSampleResumeText(file.name);
                resolve(sampleText);
            };
            reader.readAsText(file);
        });
    }

    generateSampleResumeText(fileName) {
        // Generate realistic sample resume content for demonstration
        const samples = [
            `John Doe
Software Engineer
Email: <EMAIL> | Phone: ******-0123
LinkedIn: linkedin.com/in/johndoe

PROFESSIONAL SUMMARY
Experienced Full Stack Developer with 5+ years in web development, specializing in React, Node.js, and cloud technologies. Proven track record of delivering scalable applications and leading development teams.

TECHNICAL SKILLS
• Programming Languages: JavaScript, Python, Java, TypeScript
• Frontend: React, Vue.js, Angular, HTML5, CSS3, Bootstrap
• Backend: Node.js, Express.js, Django, Spring Boot
• Databases: MongoDB, PostgreSQL, MySQL, Redis
• Cloud: AWS, Azure, Docker, Kubernetes
• Tools: Git, Jenkins, JIRA, VS Code

WORK EXPERIENCE
Senior Software Engineer | TechCorp Inc. | 2021 - Present
• Led development of microservices architecture serving 1M+ users
• Implemented CI/CD pipelines reducing deployment time by 60%
• Mentored junior developers and conducted code reviews

Software Developer | StartupXYZ | 2019 - 2021
• Built responsive web applications using React and Node.js
• Optimized database queries improving performance by 40%
• Collaborated with cross-functional teams in Agile environment

EDUCATION
Bachelor of Science in Computer Science
University of Technology | 2015 - 2019
GPA: 3.8/4.0

CERTIFICATIONS
• AWS Certified Solutions Architect
• Google Cloud Professional Developer
• Scrum Master Certification`,

            `Sarah Johnson
Data Scientist
Email: <EMAIL> | Phone: ******-0456
GitHub: github.com/sarahjohnson

PROFESSIONAL SUMMARY
Data Scientist with 4+ years of experience in machine learning, statistical analysis, and big data processing. Expert in Python, R, and cloud-based analytics platforms.

TECHNICAL SKILLS
• Programming: Python, R, SQL, Scala
• Machine Learning: TensorFlow, PyTorch, Scikit-learn, Keras
• Data Processing: Pandas, NumPy, Apache Spark, Hadoop
• Visualization: Matplotlib, Seaborn, Tableau, Power BI
• Cloud: AWS, GCP, Azure ML
• Databases: PostgreSQL, MongoDB, Cassandra

WORK EXPERIENCE
Senior Data Scientist | DataTech Solutions | 2022 - Present
• Developed predictive models improving customer retention by 25%
• Built real-time recommendation systems using collaborative filtering
• Led data science team of 5 members on multiple projects

Data Analyst | Analytics Corp | 2020 - 2022
• Performed statistical analysis on large datasets (10M+ records)
• Created automated reporting dashboards using Python and Tableau
• Collaborated with business stakeholders to define KPIs

EDUCATION
Master of Science in Data Science
Data University | 2018 - 2020

Bachelor of Science in Statistics
Math College | 2014 - 2018

CERTIFICATIONS
• Google Cloud Professional Data Engineer
• Microsoft Azure Data Scientist Associate
• Tableau Desktop Specialist`
        ];

        return samples[Math.floor(Math.random() * samples.length)];
    }

    parseResumeContent(text) {
        console.log('📊 Parsing resume content...');

        const parsed = {
            personalInfo: this.extractPersonalInfo(text),
            skills: this.extractSkills(text),
            experience: this.extractExperience(text),
            education: this.extractEducation(text),
            certifications: this.extractCertifications(text),
            summary: this.extractSummary(text)
        };

        return parsed;
    }

    extractPersonalInfo(text) {
        const emailRegex = /[\w\.-]+@[\w\.-]+\.\w+/g;
        const phoneRegex = /[\+]?[1-9]?[\-\s]?[\(]?[0-9]{3}[\)]?[\-\s]?[0-9]{3}[\-\s]?[0-9]{4}/g;
        const nameRegex = /^([A-Z][a-z]+ [A-Z][a-z]+)/m;

        return {
            name: text.match(nameRegex)?.[1] || 'Not found',
            email: text.match(emailRegex)?.[0] || 'Not found',
            phone: text.match(phoneRegex)?.[0] || 'Not found'
        };
    }

    extractSkills(text) {
        const skillKeywords = [
            // Programming Languages
            'JavaScript', 'Python', 'Java', 'C++', 'C#', 'PHP', 'Ruby', 'Go', 'Rust', 'TypeScript',
            // Frontend
            'React', 'Angular', 'Vue.js', 'HTML5', 'CSS3', 'Bootstrap', 'Tailwind', 'jQuery',
            // Backend
            'Node.js', 'Express.js', 'Django', 'Flask', 'Spring Boot', 'Laravel', 'Ruby on Rails',
            // Databases
            'MongoDB', 'PostgreSQL', 'MySQL', 'Redis', 'Cassandra', 'Oracle', 'SQLite',
            // Cloud & DevOps
            'AWS', 'Azure', 'GCP', 'Docker', 'Kubernetes', 'Jenkins', 'Git', 'CI/CD',
            // Data Science
            'Machine Learning', 'TensorFlow', 'PyTorch', 'Pandas', 'NumPy', 'Scikit-learn',
            // Other
            'Agile', 'Scrum', 'JIRA', 'REST API', 'GraphQL', 'Microservices'
        ];

        const foundSkills = skillKeywords.filter(skill =>
            text.toLowerCase().includes(skill.toLowerCase())
        );

        return {
            technical: foundSkills,
            count: foundSkills.length,
            categories: this.categorizeSkills(foundSkills)
        };
    }

    categorizeSkills(skills) {
        const categories = {
            'Programming Languages': ['JavaScript', 'Python', 'Java', 'C++', 'C#', 'PHP', 'Ruby', 'Go', 'Rust', 'TypeScript'],
            'Frontend Technologies': ['React', 'Angular', 'Vue.js', 'HTML5', 'CSS3', 'Bootstrap', 'Tailwind', 'jQuery'],
            'Backend Technologies': ['Node.js', 'Express.js', 'Django', 'Flask', 'Spring Boot', 'Laravel', 'Ruby on Rails'],
            'Databases': ['MongoDB', 'PostgreSQL', 'MySQL', 'Redis', 'Cassandra', 'Oracle', 'SQLite'],
            'Cloud & DevOps': ['AWS', 'Azure', 'GCP', 'Docker', 'Kubernetes', 'Jenkins', 'Git', 'CI/CD'],
            'Data Science & ML': ['Machine Learning', 'TensorFlow', 'PyTorch', 'Pandas', 'NumPy', 'Scikit-learn']
        };

        const result = {};
        for (const [category, categorySkills] of Object.entries(categories)) {
            result[category] = skills.filter(skill =>
                categorySkills.some(catSkill => catSkill.toLowerCase() === skill.toLowerCase())
            );
        }

        return result;
    }

    extractExperience(text) {
        const experienceSection = text.match(/WORK EXPERIENCE([\s\S]*?)(?=EDUCATION|CERTIFICATIONS|$)/i);
        if (!experienceSection) return { years: 0, positions: [] };

        const yearMatches = experienceSection[1].match(/(\d{4})\s*-\s*(\d{4}|Present)/g) || [];
        const totalYears = this.calculateTotalExperience(yearMatches);

        const positions = this.extractPositions(experienceSection[1]);

        return {
            years: totalYears,
            positions: positions,
            seniority: this.determineSeniority(totalYears)
        };
    }

    calculateTotalExperience(yearMatches) {
        let totalMonths = 0;
        const currentYear = new Date().getFullYear();

        yearMatches.forEach(match => {
            const [start, end] = match.split(/\s*-\s*/);
            const startYear = parseInt(start);
            const endYear = end === 'Present' ? currentYear : parseInt(end);
            totalMonths += (endYear - startYear) * 12;
        });

        return Math.round(totalMonths / 12 * 10) / 10; // Round to 1 decimal
    }

    extractPositions(experienceText) {
        const positionRegex = /([A-Z][a-zA-Z\s]+)\s*\|\s*([A-Z][a-zA-Z\s&.,]+)\s*\|\s*(\d{4}\s*-\s*(?:\d{4}|Present))/g;
        const positions = [];
        let match;

        while ((match = positionRegex.exec(experienceText)) !== null) {
            positions.push({
                title: match[1].trim(),
                company: match[2].trim(),
                duration: match[3].trim()
            });
        }

        return positions;
    }

    determineSeniority(years) {
        if (years < 2) return 'Junior';
        if (years < 5) return 'Mid-level';
        if (years < 8) return 'Senior';
        return 'Lead/Principal';
    }

    extractEducation(text) {
        const educationSection = text.match(/EDUCATION([\s\S]*?)(?=CERTIFICATIONS|WORK EXPERIENCE|$)/i);
        if (!educationSection) return { degree: 'Not found', institution: 'Not found' };

        const degreeRegex = /(Bachelor|Master|PhD|Associate).*?(?:in|of)\s+([A-Za-z\s]+)/i;
        const institutionRegex = /([A-Z][a-zA-Z\s&.,]+(?:University|College|Institute|School))/i;

        const degreeMatch = educationSection[1].match(degreeRegex);
        const institutionMatch = educationSection[1].match(institutionRegex);

        return {
            degree: degreeMatch ? `${degreeMatch[1]} in ${degreeMatch[2].trim()}` : 'Not found',
            institution: institutionMatch ? institutionMatch[1].trim() : 'Not found'
        };
    }

    extractCertifications(text) {
        const certSection = text.match(/CERTIFICATIONS?([\s\S]*?)(?=EDUCATION|WORK EXPERIENCE|$)/i);
        if (!certSection) return [];

        const certLines = certSection[1].split('\n').filter(line => line.trim().startsWith('•') || line.trim().startsWith('-'));
        return certLines.map(line => line.replace(/^[•\-\s]+/, '').trim()).filter(cert => cert.length > 0);
    }

    extractSummary(text) {
        const summarySection = text.match(/(?:PROFESSIONAL SUMMARY|SUMMARY|OBJECTIVE)([\s\S]*?)(?=TECHNICAL SKILLS|SKILLS|WORK EXPERIENCE|EDUCATION)/i);
        return summarySection ? summarySection[1].trim() : 'Not found';
    }

    analyzeSkills(parsedData) {
        const skills = parsedData.skills;
        const categories = skills.categories;

        return {
            totalSkills: skills.count,
            diversity: Object.keys(categories).filter(cat => categories[cat].length > 0).length,
            strengths: this.identifyStrengths(categories),
            gaps: this.identifySkillGaps(categories),
            marketability: this.calculateMarketability(skills.technical)
        };
    }

    identifyStrengths(categories) {
        const strengths = [];
        for (const [category, skills] of Object.entries(categories)) {
            if (skills.length >= 3) {
                strengths.push({
                    category,
                    skillCount: skills.length,
                    skills: skills
                });
            }
        }
        return strengths.sort((a, b) => b.skillCount - a.skillCount);
    }

    identifySkillGaps(categories) {
        const commonRequirements = {
            'Programming Languages': ['JavaScript', 'Python'],
            'Frontend Technologies': ['React', 'HTML5', 'CSS3'],
            'Backend Technologies': ['Node.js', 'Express.js'],
            'Databases': ['MongoDB', 'PostgreSQL'],
            'Cloud & DevOps': ['AWS', 'Docker', 'Git']
        };

        const gaps = [];
        for (const [category, required] of Object.entries(commonRequirements)) {
            const hasSkills = categories[category] || [];
            const missing = required.filter(skill =>
                !hasSkills.some(hasSkill => hasSkill.toLowerCase() === skill.toLowerCase())
            );
            if (missing.length > 0) {
                gaps.push({ category, missing });
            }
        }

        return gaps;
    }

    calculateMarketability(skills) {
        const highDemandSkills = ['React', 'Python', 'AWS', 'JavaScript', 'Node.js', 'Docker', 'Kubernetes', 'Machine Learning'];
        const matchCount = skills.filter(skill =>
            highDemandSkills.some(demand => demand.toLowerCase() === skill.toLowerCase())
        ).length;

        return Math.min(100, (matchCount / highDemandSkills.length) * 100);
    }

    calculateATSScore(parsedData) {
        let score = 0;
        const weights = {
            contactInfo: 15,
            skills: 25,
            experience: 20,
            education: 15,
            keywords: 15,
            formatting: 10
        };

        // Contact Information Score
        const contact = parsedData.personalInfo;
        if (contact.email !== 'Not found') score += weights.contactInfo * 0.4;
        if (contact.phone !== 'Not found') score += weights.contactInfo * 0.4;
        if (contact.name !== 'Not found') score += weights.contactInfo * 0.2;

        // Skills Score
        const skillsCount = parsedData.skills.count;
        score += Math.min(weights.skills, (skillsCount / 15) * weights.skills);

        // Experience Score
        const expYears = parsedData.experience.years;
        score += Math.min(weights.experience, (expYears / 10) * weights.experience);

        // Education Score
        if (parsedData.education.degree !== 'Not found') score += weights.education;

        // Keywords Score (based on ATS-friendly terms)
        const atsKeywords = this.atsKeywords;
        const foundKeywords = atsKeywords.filter(keyword =>
            JSON.stringify(parsedData).toLowerCase().includes(keyword.toLowerCase())
        );
        score += (foundKeywords.length / atsKeywords.length) * weights.keywords;

        // Formatting Score (assume good formatting for parsed content)
        score += weights.formatting;

        return Math.round(Math.min(100, score));
    }

    calculateJobMatch(parsedData) {
        if (!this.jobDescription) return null;

        const jobRequirements = this.parseJobDescription(this.jobDescription);
        const candidateSkills = parsedData.skills.technical;
        const candidateExp = parsedData.experience.years;

        // Skills matching
        const requiredSkills = jobRequirements.skills;
        const matchedSkills = candidateSkills.filter(skill =>
            requiredSkills.some(req => req.toLowerCase().includes(skill.toLowerCase()) ||
                                     skill.toLowerCase().includes(req.toLowerCase()))
        );
        const skillsScore = requiredSkills.length > 0 ? (matchedSkills.length / requiredSkills.length) * 100 : 0;

        // Experience matching
        const requiredExp = jobRequirements.experience;
        const expScore = candidateExp >= requiredExp ? 100 : (candidateExp / requiredExp) * 100;

        // Education matching
        const educationScore = this.matchEducation(parsedData.education, jobRequirements.education);

        // Overall match score
        const overallScore = (skillsScore * 0.5) + (expScore * 0.3) + (educationScore * 0.2);

        return {
            overallScore: Math.round(overallScore),
            skillsMatch: {
                score: Math.round(skillsScore),
                matched: matchedSkills,
                required: requiredSkills,
                missing: requiredSkills.filter(req =>
                    !matchedSkills.some(match => match.toLowerCase().includes(req.toLowerCase()))
                )
            },
            experienceMatch: {
                score: Math.round(expScore),
                candidate: candidateExp,
                required: requiredExp
            },
            educationMatch: {
                score: Math.round(educationScore)
            }
        };
    }

    parseJobDescription(jobDesc) {
        const text = jobDesc.toLowerCase();

        // Extract required skills
        const skillKeywords = this.skillsDatabase.flat();
        const foundSkills = skillKeywords.filter(skill => text.includes(skill.toLowerCase()));

        // Extract experience requirement
        const expMatch = text.match(/(\d+)\+?\s*years?\s*(?:of\s*)?experience/i);
        const requiredExp = expMatch ? parseInt(expMatch[1]) : 2;

        // Extract education requirement
        const educationReq = text.includes('bachelor') ? 'bachelor' :
                           text.includes('master') ? 'master' :
                           text.includes('phd') ? 'phd' : 'any';

        return {
            skills: foundSkills,
            experience: requiredExp,
            education: educationReq
        };
    }

    matchEducation(candidateEd, requiredEd) {
        if (requiredEd === 'any') return 100;

        const candidate = candidateEd.degree.toLowerCase();
        const required = requiredEd.toLowerCase();

        if (candidate.includes(required)) return 100;
        if (required === 'bachelor' && (candidate.includes('master') || candidate.includes('phd'))) return 100;
        if (required === 'master' && candidate.includes('phd')) return 100;

        return 50; // Partial match
    }

    generateSuggestions(parsedData, skillsAnalysis) {
        const suggestions = [];

        // Skills suggestions
        if (skillsAnalysis.gaps.length > 0) {
            suggestions.push({
                type: 'skills',
                priority: 'high',
                title: 'Skill Enhancement Opportunities',
                description: 'Consider learning these in-demand skills to improve your marketability',
                items: skillsAnalysis.gaps.flatMap(gap => gap.missing).slice(0, 5)
            });
        }

        // Experience suggestions
        if (parsedData.experience.years < 3) {
            suggestions.push({
                type: 'experience',
                priority: 'medium',
                title: 'Experience Building',
                description: 'Consider contributing to open source projects or taking on freelance work',
                items: ['Open source contributions', 'Personal projects', 'Freelance work', 'Internships']
            });
        }

        // Certification suggestions
        if (parsedData.certifications.length < 2) {
            suggestions.push({
                type: 'certifications',
                priority: 'medium',
                title: 'Professional Certifications',
                description: 'Industry certifications can significantly boost your profile',
                items: ['AWS Certified Solutions Architect', 'Google Cloud Professional', 'Microsoft Azure Fundamentals']
            });
        }

        return suggestions;
    }

    initializeSkillsDatabase() {
        return [
            ['JavaScript', 'Python', 'Java', 'C++', 'C#', 'PHP', 'Ruby', 'Go', 'Rust', 'TypeScript'],
            ['React', 'Angular', 'Vue.js', 'HTML5', 'CSS3', 'Bootstrap', 'Tailwind', 'jQuery'],
            ['Node.js', 'Express.js', 'Django', 'Flask', 'Spring Boot', 'Laravel', 'Ruby on Rails'],
            ['MongoDB', 'PostgreSQL', 'MySQL', 'Redis', 'Cassandra', 'Oracle', 'SQLite'],
            ['AWS', 'Azure', 'GCP', 'Docker', 'Kubernetes', 'Jenkins', 'Git', 'CI/CD'],
            ['Machine Learning', 'TensorFlow', 'PyTorch', 'Pandas', 'NumPy', 'Scikit-learn']
        ];
    }

    initializeIndustryKeywords() {
        return {
            'Technology': ['software', 'development', 'programming', 'coding', 'technical', 'IT'],
            'Data Science': ['analytics', 'machine learning', 'AI', 'statistics', 'data mining'],
            'Cloud Computing': ['cloud', 'AWS', 'Azure', 'GCP', 'serverless', 'microservices'],
            'DevOps': ['CI/CD', 'automation', 'deployment', 'infrastructure', 'monitoring']
        };
    }

    initializeATSKeywords() {
        return [
            'experience', 'skills', 'education', 'certification', 'project', 'team', 'leadership',
            'management', 'development', 'analysis', 'implementation', 'optimization', 'collaboration'
        ];
    }

    loadSampleJobDescription() {
        const sampleJob = `Senior Full Stack Developer

We are seeking an experienced Full Stack Developer to join our dynamic team. The ideal candidate will have 5+ years of experience in web development and a strong background in modern technologies.

Required Skills:
• 5+ years of experience in JavaScript and Python
• Proficiency in React, Node.js, and Express.js
• Experience with MongoDB and PostgreSQL databases
• Knowledge of AWS cloud services
• Familiarity with Docker and CI/CD pipelines
• Strong understanding of RESTful APIs and microservices architecture

Education:
• Bachelor's degree in Computer Science or related field

Responsibilities:
• Develop and maintain scalable web applications
• Collaborate with cross-functional teams
• Implement best practices for code quality and testing
• Participate in code reviews and technical discussions`;

        const jobDescInput = document.getElementById('jobDescription');
        if (jobDescInput && !jobDescInput.value.trim()) {
            jobDescInput.value = sampleJob;
            this.jobDescription = sampleJob;
        }
    }

    showLoading() {
        const loadingDiv = document.getElementById('loadingIndicator') || this.createLoadingIndicator();
        loadingDiv.style.display = 'block';
    }

    hideLoading() {
        const loadingDiv = document.getElementById('loadingIndicator');
        if (loadingDiv) loadingDiv.style.display = 'none';
    }

    createLoadingIndicator() {
        const loading = document.createElement('div');
        loading.id = 'loadingIndicator';
        loading.innerHTML = `
            <div class="text-center p-4">
                <div class="spinner-border text-primary" role="status">
                    <span class="visually-hidden">Loading...</span>
                </div>
                <p class="mt-2">🤖 AI is analyzing your resume...</p>
            </div>
        `;
        document.body.appendChild(loading);
        return loading;
    }

    updateFileDisplay(files) {
        const fileList = document.getElementById('fileList') || this.createFileListElement();
        fileList.innerHTML = files.map(file => `
            <div class="file-item p-2 border rounded mb-2">
                <i class="fas fa-file-pdf text-danger me-2"></i>
                <span>${file.name}</span>
                <small class="text-muted ms-2">(${this.formatFileSize(file.size)})</small>
            </div>
        `).join('');
    }

    createFileListElement() {
        const fileList = document.createElement('div');
        fileList.id = 'fileList';
        fileList.className = 'mt-3';

        const uploadArea = document.getElementById('uploadArea');
        if (uploadArea) {
            uploadArea.parentNode.insertBefore(fileList, uploadArea.nextSibling);
        }

        return fileList;
    }

    formatFileSize(bytes) {
        if (bytes === 0) return '0 Bytes';
        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }

    showAnalysisResults() {
        console.log('📊 Displaying analysis results...');

        const resultsContainer = document.getElementById('analysisResults') || this.createResultsContainer();
        resultsContainer.innerHTML = this.generateResultsHTML();
        resultsContainer.style.display = 'block';

        // Scroll to results
        resultsContainer.scrollIntoView({ behavior: 'smooth' });
    }

    createResultsContainer() {
        const container = document.createElement('div');
        container.id = 'analysisResults';
        container.className = 'mt-4';

        const mainContent = document.querySelector('.container') || document.body;
        mainContent.appendChild(container);

        return container;
    }

    generateResultsHTML() {
        if (this.analysisResults.length === 0) return '<p>No results to display.</p>';

        return this.analysisResults.map((result, index) => `
            <div class="card mb-4 shadow-sm">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0">
                        <i class="fas fa-file-alt me-2"></i>
                        Analysis Results: ${result.fileName}
                    </h5>
                </div>
                <div class="card-body">
                    ${this.generateScoreCards(result)}
                    ${this.generateDetailedAnalysis(result)}
                    ${this.generateJobMatchSection(result)}
                    ${this.generateSuggestionsSection(result)}
                </div>
            </div>
        `).join('');
    }

    generateScoreCards(result) {
        const atsScore = result.atsScore;
        const jobMatch = result.jobMatch;
        const marketability = result.skillsAnalysis.marketability;

        return `
            <div class="row mb-4">
                <div class="col-md-4">
                    <div class="card text-center border-primary">
                        <div class="card-body">
                            <h3 class="text-primary">${atsScore}%</h3>
                            <p class="card-text">ATS Compatibility</p>
                            <div class="progress">
                                <div class="progress-bar bg-primary" style="width: ${atsScore}%"></div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="card text-center border-success">
                        <div class="card-body">
                            <h3 class="text-success">${Math.round(marketability)}%</h3>
                            <p class="card-text">Market Relevance</p>
                            <div class="progress">
                                <div class="progress-bar bg-success" style="width: ${marketability}%"></div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="card text-center border-info">
                        <div class="card-body">
                            <h3 class="text-info">${jobMatch ? jobMatch.overallScore : 'N/A'}%</h3>
                            <p class="card-text">Job Match</p>
                            <div class="progress">
                                <div class="progress-bar bg-info" style="width: ${jobMatch ? jobMatch.overallScore : 0}%"></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `;
    }

    generateDetailedAnalysis(result) {
        const { parsedData, skillsAnalysis } = result;

        return `
            <div class="row">
                <div class="col-md-6">
                    <h6><i class="fas fa-user me-2"></i>Personal Information</h6>
                    <ul class="list-unstyled">
                        <li><strong>Name:</strong> ${parsedData.personalInfo.name}</li>
                        <li><strong>Email:</strong> ${parsedData.personalInfo.email}</li>
                        <li><strong>Phone:</strong> ${parsedData.personalInfo.phone}</li>
                    </ul>

                    <h6><i class="fas fa-briefcase me-2"></i>Experience</h6>
                    <ul class="list-unstyled">
                        <li><strong>Total Experience:</strong> ${parsedData.experience.years} years</li>
                        <li><strong>Seniority Level:</strong> ${parsedData.experience.seniority}</li>
                        <li><strong>Positions:</strong> ${parsedData.experience.positions.length}</li>
                    </ul>
                </div>
                <div class="col-md-6">
                    <h6><i class="fas fa-cogs me-2"></i>Skills Analysis</h6>
                    <ul class="list-unstyled">
                        <li><strong>Total Skills:</strong> ${skillsAnalysis.totalSkills}</li>
                        <li><strong>Skill Diversity:</strong> ${skillsAnalysis.diversity} categories</li>
                        <li><strong>Top Strengths:</strong></li>
                        <ul>
                            ${skillsAnalysis.strengths.slice(0, 3).map(strength =>
                                `<li>${strength.category} (${strength.skillCount} skills)</li>`
                            ).join('')}
                        </ul>
                    </ul>

                    <h6><i class="fas fa-graduation-cap me-2"></i>Education</h6>
                    <ul class="list-unstyled">
                        <li><strong>Degree:</strong> ${parsedData.education.degree}</li>
                        <li><strong>Institution:</strong> ${parsedData.education.institution}</li>
                    </ul>
                </div>
            </div>
        `;
    }

    generateJobMatchSection(result) {
        if (!result.jobMatch) {
            return `
                <div class="alert alert-info">
                    <i class="fas fa-info-circle me-2"></i>
                    Add a job description to see detailed job matching analysis.
                </div>
            `;
        }

        const jobMatch = result.jobMatch;

        return `
            <div class="mt-4">
                <h6><i class="fas fa-bullseye me-2"></i>Job Matching Analysis</h6>
                <div class="row">
                    <div class="col-md-4">
                        <div class="card border-primary">
                            <div class="card-body text-center">
                                <h5 class="text-primary">${jobMatch.skillsMatch.score}%</h5>
                                <p class="card-text">Skills Match</p>
                                <small class="text-muted">${jobMatch.skillsMatch.matched.length}/${jobMatch.skillsMatch.required.length} skills matched</small>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="card border-success">
                            <div class="card-body text-center">
                                <h5 class="text-success">${jobMatch.experienceMatch.score}%</h5>
                                <p class="card-text">Experience Match</p>
                                <small class="text-muted">${jobMatch.experienceMatch.candidate} vs ${jobMatch.experienceMatch.required} years required</small>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="card border-info">
                            <div class="card-body text-center">
                                <h5 class="text-info">${jobMatch.educationMatch.score}%</h5>
                                <p class="card-text">Education Match</p>
                                <small class="text-muted">Qualification alignment</small>
                            </div>
                        </div>
                    </div>
                </div>

                ${jobMatch.skillsMatch.missing.length > 0 ? `
                    <div class="alert alert-warning mt-3">
                        <strong>Missing Skills:</strong> ${jobMatch.skillsMatch.missing.join(', ')}
                    </div>
                ` : ''}

                ${jobMatch.skillsMatch.matched.length > 0 ? `
                    <div class="alert alert-success mt-3">
                        <strong>Matched Skills:</strong> ${jobMatch.skillsMatch.matched.join(', ')}
                    </div>
                ` : ''}
            </div>
        `;
    }

    generateSuggestionsSection(result) {
        if (!result.suggestions || result.suggestions.length === 0) {
            return '';
        }

        return `
            <div class="mt-4">
                <h6><i class="fas fa-lightbulb me-2"></i>Improvement Suggestions</h6>
                ${result.suggestions.map(suggestion => `
                    <div class="card mb-3 border-${suggestion.priority === 'high' ? 'warning' : 'info'}">
                        <div class="card-body">
                            <h6 class="card-title">
                                <span class="badge bg-${suggestion.priority === 'high' ? 'warning' : 'info'} me-2">
                                    ${suggestion.priority.toUpperCase()}
                                </span>
                                ${suggestion.title}
                            </h6>
                            <p class="card-text">${suggestion.description}</p>
                            <ul class="list-unstyled">
                                ${suggestion.items.map(item => `<li><i class="fas fa-check-circle text-success me-2"></i>${item}</li>`).join('')}
                            </ul>
                        </div>
                    </div>
                `).join('')}
            </div>
        `;
    }

    showError(message) {
        const errorDiv = document.getElementById('errorMessage') || this.createErrorElement();
        errorDiv.innerHTML = `
            <div class="alert alert-danger alert-dismissible fade show" role="alert">
                <i class="fas fa-exclamation-triangle me-2"></i>
                ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        `;
        errorDiv.style.display = 'block';

        // Auto-hide after 5 seconds
        setTimeout(() => {
            errorDiv.style.display = 'none';
        }, 5000);
    }

    createErrorElement() {
        const errorDiv = document.createElement('div');
        errorDiv.id = 'errorMessage';
        errorDiv.className = 'mt-3';

        const uploadArea = document.getElementById('uploadArea');
        if (uploadArea) {
            uploadArea.parentNode.insertBefore(errorDiv, uploadArea);
        }

        return errorDiv;
    }

    // Export functionality
    exportResults(format = 'json') {
        if (this.analysisResults.length === 0) {
            this.showError('No analysis results to export.');
            return;
        }

        const data = {
            timestamp: new Date().toISOString(),
            totalAnalyzed: this.analysisResults.length,
            results: this.analysisResults
        };

        if (format === 'json') {
            this.downloadJSON(data, 'resume-analysis-results.json');
        } else if (format === 'csv') {
            this.downloadCSV(data, 'resume-analysis-results.csv');
        }
    }

    downloadJSON(data, filename) {
        const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' });
        this.downloadBlob(blob, filename);
    }

    downloadCSV(data, filename) {
        const headers = ['File Name', 'ATS Score', 'Job Match', 'Experience Years', 'Skills Count', 'Marketability'];
        const rows = data.results.map(result => [
            result.fileName,
            result.atsScore,
            result.jobMatch ? result.jobMatch.overallScore : 'N/A',
            result.parsedData.experience.years,
            result.skillsAnalysis.totalSkills,
            Math.round(result.skillsAnalysis.marketability)
        ]);

        const csvContent = [headers, ...rows].map(row => row.join(',')).join('\n');
        const blob = new Blob([csvContent], { type: 'text/csv' });
        this.downloadBlob(blob, filename);
    }

    downloadBlob(blob, filename) {
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = filename;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        URL.revokeObjectURL(url);
    }

    // Clear all results
    clearResults() {
        this.analysisResults = [];
        this.uploadedFiles = [];

        const resultsContainer = document.getElementById('analysisResults');
        if (resultsContainer) {
            resultsContainer.style.display = 'none';
            resultsContainer.innerHTML = '';
        }

        const fileList = document.getElementById('fileList');
        if (fileList) {
            fileList.innerHTML = '';
        }

        const fileInput = document.getElementById('fileInput');
        if (fileInput) {
            fileInput.value = '';
        }

        console.log('🧹 Results cleared');
    }
}

// Initialize the analyzer when the page loads
document.addEventListener('DOMContentLoaded', () => {
    window.cubeAIAnalyzer = new CubeAIResumeAnalyzer();
    console.log('✅ CubeAI Resume Analyzer initialized successfully');
});

// Export for use in other modules
if (typeof module !== 'undefined' && module.exports) {
    module.exports = CubeAIResumeAnalyzer;
}

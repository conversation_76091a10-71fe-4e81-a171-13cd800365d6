<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Cube AI Solutions - Recruitment Portal</title>
    
    <!-- Favicon -->
    <link rel="icon" href="data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 100'><text y='.9em' font-size='90'>🔮</text></svg>">
    
    <!-- External CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.0/font/bootstrap-icons.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
    
    <!-- Custom Styles -->
    <style>
        :root {
            --primary-blue: #2563eb;
            --primary-blue-dark: #1d4ed8;
            --primary-blue-light: #3b82f6;
            --success-green: #10b981;
            --warning-orange: #f59e0b;
            --danger-red: #ef4444;
            --info-cyan: #06b6d4;
            --white: #ffffff;
            --gray-50: #f9fafb;
            --gray-100: #f3f4f6;
            --gray-200: #e5e7eb;
            --gray-300: #d1d5db;
            --gray-400: #9ca3af;
            --gray-500: #6b7280;
            --gray-600: #4b5563;
            --gray-700: #374151;
            --gray-800: #1f2937;
            --gray-900: #111827;
            --header-height: 70px;
            --nav-height: 56px;
            --transition-base: 250ms ease-in-out;
            --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
            --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
            --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background-color: var(--gray-50);
            color: var(--gray-700);
            line-height: 1.6;
            overflow-x: hidden;
        }

        /* Enhanced Loading Screen */
        .loading-overlay {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 9999;
            transition: opacity 0.5s ease-out;
        }

        .loading-content {
            text-align: center;
            color: white;
        }

        .loading-spinner .spinner-border {
            width: 3rem;
            height: 3rem;
            border-width: 3px;
        }

        .loading-overlay.fade-out {
            opacity: 0;
            pointer-events: none;
        }

        /* Enhanced Login Page */
        .login-container {
            min-height: 100vh;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 2rem;
            position: relative;
            overflow: hidden;
        }

        .login-container::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 100'%3E%3Cdefs%3E%3Cpattern id='grid' width='10' height='10' patternUnits='userSpaceOnUse'%3E%3Cpath d='M 10 0 L 0 0 0 10' fill='none' stroke='rgba(255,255,255,0.1)' stroke-width='0.5'/%3E%3C/pattern%3E%3C/defs%3E%3Crect width='100' height='100' fill='url(%23grid)'/%3E%3C/svg%3E");
            opacity: 0.3;
        }

        .login-wrapper {
            display: grid;
            grid-template-columns: 1fr 450px;
            gap: 4rem;
            max-width: 1400px;
            width: 100%;
            align-items: center;
            position: relative;
            z-index: 1;
        }

        .login-brand {
            color: white;
            padding: 2rem;
        }

        .brand-content {
            text-align: center;
            margin-bottom: 3rem;
        }

        .company-logo {
            font-size: 5rem;
            margin-bottom: 1.5rem;
            animation: float 3s ease-in-out infinite;
        }

        @keyframes float {
            0%, 100% { transform: translate3d(0, 0, 0); }
            50% { transform: translate3d(0, -10px, 0); }
        }

        .company-name {
            font-size: 3rem;
            font-weight: 800;
            margin-bottom: 0.5rem;
            text-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        .company-subtitle {
            font-size: 1.25rem;
            opacity: 0.9;
            margin-bottom: 2rem;
        }

        .brand-divider {
            width: 80px;
            height: 4px;
            background: white;
            margin: 0 auto 2rem;
            border-radius: 2px;
        }

        .portal-title {
            font-size: 2rem;
            font-weight: 700;
            margin-bottom: 0.5rem;
        }

        .portal-description {
            font-size: 1.125rem;
            opacity: 0.85;
            margin-bottom: 3rem;
        }

        /* Enhanced Features Grid */
        .features-showcase h3 {
            font-size: 1.5rem;
            font-weight: 600;
            margin-bottom: 2rem;
            text-align: center;
        }

        .feature-grid {
            display: grid;
            gap: 1.5rem;
        }

        .feature-item {
            display: flex;
            align-items: flex-start;
            gap: 1rem;
            padding: 1.5rem;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 16px;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            transition: all 0.3s ease;
            transform: translate3d(0, 0, 0);
        }

        .feature-item:hover {
            background: rgba(255, 255, 255, 0.15);
            transform: translate3d(0, -3px, 0);
            box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
        }

        .feature-item i {
            font-size: 1.75rem;
            margin-top: 0.25rem;
            opacity: 0.9;
        }

        /* Enhanced Login Card */
        .login-card {
            background: white;
            border-radius: 24px;
            box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
            overflow: hidden;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .login-header {
            padding: 3rem 3rem 2rem;
            text-align: center;
            background: linear-gradient(180deg, var(--gray-50) 0%, white 100%);
            border-bottom: 1px solid var(--gray-100);
        }

        .login-header h2 {
            font-size: 2rem;
            font-weight: 700;
            color: var(--gray-800);
            margin-bottom: 0.5rem;
        }

        .login-header p {
            color: var(--gray-500);
            font-size: 1rem;
        }

        .login-form {
            padding: 3rem;
        }

        .form-group {
            margin-bottom: 1.5rem;
        }

        .form-label {
            display: flex;
            align-items: center;
            font-weight: 500;
            color: var(--gray-700);
            margin-bottom: 0.75rem;
            font-size: 0.875rem;
        }

        .form-control {
            width: 100%;
            padding: 0.875rem 1rem;
            border: 2px solid var(--gray-200);
            border-radius: 12px;
            font-size: 1rem;
            transition: all 0.2s ease;
            background: white;
            font-family: inherit;
        }

        .form-control:focus {
            outline: none;
            border-color: var(--primary-blue);
            box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
        }

        .password-input-group {
            position: relative;
        }

        .password-toggle {
            position: absolute;
            right: 12px;
            top: 50%;
            transform: translateY(-50%);
            background: none;
            border: none;
            color: var(--gray-400);
            cursor: pointer;
            padding: 0.5rem;
            border-radius: 8px;
            transition: all 0.2s ease;
            z-index: 2;
        }

        .password-toggle:hover {
            color: var(--primary-blue);
            background: var(--gray-50);
        }

        .form-options {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 2rem;
            flex-wrap: wrap;
            gap: 1rem;
        }

        .btn-login {
            width: 100%;
            padding: 1rem 1.5rem;
            background: linear-gradient(135deg, var(--primary-blue) 0%, var(--primary-blue-dark) 100%);
            color: white;
            border: none;
            border-radius: 12px;
            font-size: 1rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
            margin-bottom: 1.5rem;
            transform: translate3d(0, 0, 0);
        }

        .btn-login:hover:not(:disabled) {
            transform: translate3d(0, -2px, 0);
            box-shadow: 0 10px 25px -5px rgba(37, 99, 235, 0.5);
        }

        .btn-login:disabled {
            opacity: 0.7;
            cursor: not-allowed;
            transform: translate3d(0, 0, 0);
        }

        /* Enhanced Main Application */
        .main-application {
            min-height: 100vh;
            background: var(--gray-50);
        }

        .top-header {
            background: white;
            border-bottom: 1px solid var(--gray-200);
            height: var(--header-height);
            position: sticky;
            top: 0;
            z-index: 1020;
            box-shadow: var(--shadow-sm);
        }

        .header-content {
            height: 100%;
            padding: 0 2rem;
            display: flex;
            align-items: center;
            justify-content: space-between;
            max-width: 1600px;
            margin: 0 auto;
        }

        .app-brand {
            display: flex;
            align-items: center;
            gap: 0.75rem;
            font-weight: 700;
            font-size: 1.25rem;
            color: var(--primary-blue);
        }

        .app-brand i {
            font-size: 2rem;
        }

        /* Enhanced Search */
        .header-center {
            flex: 1;
            max-width: 600px;
            margin: 0 2rem;
        }

        .search-input-group {
            position: relative;
        }

        .global-search {
            width: 100%;
            padding: 0.75rem 1rem 0.75rem 3rem;
            border: 1px solid var(--gray-200);
            border-radius: 25px;
            font-size: 0.875rem;
            background: var(--gray-50);
            transition: all 0.2s ease;
        }

        .global-search:focus {
            outline: none;
            border-color: var(--primary-blue);
            background: white;
            box-shadow: var(--shadow-sm);
        }

        .search-icon {
            position: absolute;
            left: 12px;
            top: 50%;
            transform: translateY(-50%);
            color: var(--gray-400);
            z-index: 1;
        }

        /* Enhanced Navigation */
        .main-navigation {
            background: white;
            border-bottom: 1px solid var(--gray-200);
            height: var(--nav-height);
            position: sticky;
            top: var(--header-height);
            z-index: 1010;
        }

        .nav-container {
            height: 100%;
            padding: 0 2rem;
            display: flex;
            align-items: center;
            justify-content: space-between;
            max-width: 1600px;
            margin: 0 auto;
        }

        .nav-tabs-wrapper {
            display: flex;
            height: 100%;
            gap: 0;
            overflow-x: auto;
            scrollbar-width: none;
        }

        .nav-tab {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            padding: 0 1.5rem;
            height: 100%;
            background: none;
            border: none;
            border-bottom: 3px solid transparent;
            color: var(--gray-600);
            font-size: 0.875rem;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.2s ease;
            white-space: nowrap;
            min-width: 44px;
        }

        .nav-tab:hover {
            background: var(--gray-50);
            color: var(--primary-blue);
        }

        .nav-tab.active {
            color: var(--primary-blue);
            border-bottom-color: var(--primary-blue);
            background: var(--gray-50);
        }

        .tab-badge {
            background: var(--primary-blue);
            color: white;
            font-size: 10px;
            font-weight: 600;
            padding: 2px 6px;
            border-radius: 10px;
            min-width: 18px;
            text-align: center;
        }

        /* Enhanced Dashboard */
        .main-content {
            padding: 2rem;
            max-width: 1600px;
            margin: 0 auto;
            min-height: calc(100vh - var(--header-height) - var(--nav-height));
        }

        .module-header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: 2rem;
            background: white;
            padding: 2rem;
            border-radius: 16px;
            box-shadow: var(--shadow-sm);
            border: 1px solid var(--gray-100);
            flex-wrap: wrap;
            gap: 1rem;
        }

        .header-info h1 {
            font-size: 2.5rem;
            font-weight: 700;
            color: var(--gray-800);
            margin-bottom: 0.5rem;
            display: flex;
            align-items: center;
        }

        .dashboard-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 2rem;
        }

        .widget {
            background: white;
            border-radius: 16px;
            box-shadow: var(--shadow-sm);
            border: 1px solid var(--gray-100);
            overflow: hidden;
            transition: all 0.3s ease;
            transform: translate3d(0, 0, 0);
        }

        .widget:hover {
            box-shadow: var(--shadow-lg);
            transform: translate3d(0, -2px, 0);
        }

        .widget-header {
            padding: 1.5rem 1.5rem 1rem;
            border-bottom: 1px solid var(--gray-100);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .widget-content {
            padding: 1.5rem;
        }

        /* Enhanced Stats Cards */
        .stats-widget {
            grid-column: 1 / -1;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 1.5rem;
        }

        .stat-card {
            display: flex;
            align-items: center;
            gap: 1rem;
            padding: 1.5rem;
            border-radius: 16px;
            border: 1px solid var(--gray-100);
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
            transform: translate3d(0, 0, 0);
        }

        .stat-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: var(--card-color);
        }

        .stat-card.primary { --card-color: var(--primary-blue); }
        .stat-card.success { --card-color: var(--success-green); }
        .stat-card.warning { --card-color: var(--warning-orange); }
        .stat-card.info { --card-color: var(--info-cyan); }

        .stat-card:hover {
            transform: translate3d(0, -2px, 0);
            box-shadow: var(--shadow-md);
        }

        .stat-icon {
            width: 60px;
            height: 60px;
            border-radius: 16px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.5rem;
            color: white;
            background: var(--card-color);
        }

        .stat-details h4 {
            font-size: 2.5rem;
            font-weight: 800;
            color: var(--gray-800);
            margin-bottom: 0.25rem;
            line-height: 1;
        }

        .stat-label {
            color: var(--gray-500);
            font-size: 0.875rem;
            margin-bottom: 0.5rem;
            font-weight: 500;
        }

        .stat-trend {
            font-size: 0.75rem;
            font-weight: 600;
            display: flex;
            align-items: center;
            gap: 0.25rem;
        }

        .stat-trend.positive { color: var(--success-green); }
        .stat-trend.negative { color: var(--danger-red); }

        /* Enhanced Activity Timeline */
        .activity-item {
            display: flex;
            align-items: center;
            gap: 1rem;
            padding: 1rem;
            border-radius: 12px;
            border: 1px solid var(--gray-100);
            transition: all 0.2s ease;
            margin-bottom: 1rem;
        }

        .activity-item:hover {
            background: var(--gray-50);
            box-shadow: var(--shadow-sm);
        }

        .activity-avatar {
            position: relative;
            flex-shrink: 0;
        }

        .activity-avatar img {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            object-fit: cover;
        }

        .status-indicator {
            position: absolute;
            bottom: 0;
            right: 0;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            border: 2px solid white;
        }

        .status-indicator.online { background: var(--success-green); }

        /* Quick Actions Enhancement */
        .quick-actions-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 1rem;
        }

        .quick-action-card {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 0.75rem;
            padding: 1.5rem;
            border: 1px solid var(--gray-100);
            border-radius: 16px;
            background: white;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            color: var(--gray-700);
            transform: translate3d(0, 0, 0);
            min-height: 140px;
        }

        .quick-action-card:hover {
            background: var(--gray-50);
            border-color: var(--primary-blue);
            transform: translate3d(0, -3px, 0);
            box-shadow: var(--shadow-md);
            color: var(--gray-800);
        }

        .action-icon {
            width: 48px;
            height: 48px;
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.5rem;
            color: white;
        }

        .action-icon.primary { background: var(--primary-blue); }
        .action-icon.success { background: var(--success-green); }
        .action-icon.warning { background: var(--warning-orange); }
        .action-icon.info { background: var(--info-cyan); }
        .action-icon.secondary { background: var(--gray-500); }
        .action-icon.dark { background: var(--gray-700); }

        /* Enhanced Coming Soon */
        .coming-soon {
            text-align: center;
            padding: 4rem 2rem;
            background: white;
            border-radius: 16px;
            box-shadow: var(--shadow-sm);
            border: 1px solid var(--gray-100);
        }

        .coming-soon i {
            color: var(--gray-300);
            margin-bottom: 2rem;
        }

        .coming-soon h3 {
            color: var(--gray-600);
            font-size: 2rem;
            font-weight: 600;
            margin-bottom: 1rem;
        }

        .coming-soon p {
            color: var(--gray-500);
            font-size: 1rem;
            margin-bottom: 2rem;
            max-width: 400px;
            margin-left: auto;
            margin-right: auto;
        }

        /* Enhanced Buttons */
        .btn {
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            padding: 0.75rem 1.5rem;
            border-radius: 12px;
            font-weight: 500;
            font-size: 0.875rem;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            border: 1px solid;
            background: white;
            transform: translate3d(0, 0, 0);
            min-height: 44px;
        }

        .btn:hover {
            transform: translate3d(0, -1px, 0);
            box-shadow: var(--shadow-md);
        }

        .btn-primary {
            background: linear-gradient(135deg, var(--primary-blue) 0%, var(--primary-blue-dark) 100%);
            color: white;
            border-color: var(--primary-blue);
        }

        .btn-primary:hover {
            box-shadow: 0 10px 25px -5px rgba(37, 99, 235, 0.5);
            color: white;
        }

        .btn-outline-primary {
            color: var(--primary-blue);
            border-color: var(--primary-blue);
        }

        .btn-outline-primary:hover {
            background: var(--primary-blue);
            color: white;
        }

        /* User Menu Enhancements */
        .user-btn {
            display: flex;
            align-items: center;
            gap: 0.75rem;
            background: white;
            border: 1px solid var(--gray-200);
            border-radius: 12px;
            padding: 0.5rem 1rem;
            cursor: pointer;
            transition: all 0.2s ease;
        }

        .user-avatar {
            width: 32px;
            height: 32px;
            border-radius: 50%;
            object-fit: cover;
        }

        .notification-btn {
            position: relative;
            background: white;
            border: 1px solid var(--gray-200);
            border-radius: 12px;
            padding: 0.75rem;
            cursor: pointer;
            transition: all 0.2s ease;
        }

        .notification-badge {
            position: absolute;
            top: -4px;
            right: -4px;
            background: var(--danger-red);
            color: white;
            border-radius: 50%;
            width: 18px;
            height: 18px;
            font-size: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: 600;
            border: 2px solid white;
        }

        /* Action Buttons */
        .action-btn {
            width: 44px;
            height: 44px;
            border-radius: 12px;
            border: 1px solid var(--gray-200);
            background: white;
            color: var(--gray-600);
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: all 0.2s ease;
            transform: translate3d(0, 0, 0);
        }

        .action-btn:hover {
            background: var(--primary-blue);
            border-color: var(--primary-blue);
            color: white;
            transform: translate3d(0, -1px, 0);
        }

        /* Demo Credentials Styling */
        .demo-credentials {
            background: var(--gray-50);
            border: 1px solid var(--gray-200);
            border-radius: 12px;
            padding: 1rem;
            margin-bottom: 1.5rem;
        }

        .demo-title {
            font-size: 0.75rem;
            font-weight: 600;
            color: var(--gray-600);
            margin-bottom: 0.5rem;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .demo-info {
            display: flex;
            flex-direction: column;
            gap: 0.25rem;
        }

        .demo-info span {
            font-size: 0.875rem;
            color: var(--gray-600);
            font-family: 'Monaco', 'Courier New', monospace;
        }

        /* Responsive Design */
        @media (max-width: 992px) {
            .login-wrapper {
                grid-template-columns: 1fr;
                gap: 2rem;
                max-width: 500px;
            }

            .header-center {
                display: none;
            }

            .module-header {
                flex-direction: column;
                align-items: stretch;
            }

            .stats-grid {
                grid-template-columns: repeat(2, 1fr);
            }
        }

        @media (max-width: 768px) {
            .stats-grid {
                grid-template-columns: 1fr;
            }

            .quick-actions-grid {
                grid-template-columns: repeat(2, 1fr);
            }

            .user-info {
                display: none;
            }

            .nav-tab span {
                display: none;
            }

            .nav-tab {
                padding: 0 0.75rem;
                min-width: 44px;
                justify-content: center;
            }
        }

        @media (max-width: 576px) {
            .header-content,
            .nav-container,
            .main-content {
                padding-left: 1rem;
                padding-right: 1rem;
            }

            .company-name {
                font-size: 2rem;
            }

            .quick-actions-grid {
                grid-template-columns: 1fr;
            }

            .stat-card {
                flex-direction: column;
                text-align: center;
            }
        }

        /* Utility Classes */
        .d-none { display: none !important; }
        .text-muted { color: var(--gray-500) !important; }
        .fw-bold { font-weight: 700 !important; }
        .fw-semibold { font-weight: 600 !important; }

        /* Animation for module switching */
        .module-content {
            display: none;
        }

        .module-content.active {
            display: block;
            animation: fadeSlideIn 0.4s ease-out;
        }

        @keyframes fadeSlideIn {
            from {
                opacity: 0;
                transform: translate3d(0, 20px, 0);
            }
            to {
                opacity: 1;
                transform: translate3d(0, 0, 0);
            }
        }

        /* Enhanced Pipeline Chart */
        .pipeline-chart {
            display: flex;
            flex-direction: column;
            gap: 1rem;
        }

        .pipeline-stage {
            display: flex;
            flex-direction: column;
            gap: 0.5rem;
        }

        .stage-info {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .stage-count {
            font-size: 0.875rem;
            font-weight: 600;
            color: var(--primary-blue);
            background: rgba(37, 99, 235, 0.1);
            padding: 4px 8px;
            border-radius: 12px;
            min-width: 24px;
            text-align: center;
        }

        .stage-bar {
            height: 8px;
            background: var(--gray-100);
            border-radius: 4px;
            overflow: hidden;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, var(--primary-blue) 0%, var(--primary-blue-light) 100%);
            border-radius: 4px;
            transition: width 0.8s ease-out;
            width: 0%;
        }
    </style>
</head>
<body>
    <!-- Enhanced Loading Screen -->
    <div id="loading-screen" class="loading-overlay">
        <div class="loading-content">
            <div class="loading-spinner mb-4">
                <div class="spinner-border text-light" role="status">
                    <span class="visually-hidden">Loading...</span>
                </div>
            </div>
            <div class="loading-text">
                <h4>Cube AI Recruit</h4>
                <p>Initializing your AI-powered recruitment portal...</p>
            </div>
        </div>
    </div>

    <!-- Enhanced Login Page -->
    <div id="login-page" class="login-container">
        <div class="login-wrapper">
            <!-- Enhanced Left Side - Branding -->
            <div class="login-brand">
                <div class="brand-content">
                    <div class="company-logo">
                        <i class="bi bi-cube"></i>
                    </div>
                    <h1 class="company-name">Cube AI Solutions</h1>
                    <p class="company-subtitle">Private Limited</p>
                    <div class="brand-divider"></div>
                    <h2 class="portal-title">Recruitment Portal</h2>
                    <p class="portal-description">Next-Generation AI-Powered Talent Acquisition Platform</p>
                </div>
                
                <!-- Enhanced Features Showcase -->
                <div class="features-showcase">
                    <h3>Why Choose Our Platform?</h3>
                    <div class="feature-grid">
                        <div class="feature-item">
                            <i class="bi bi-robot"></i>
                            <div>
                                <strong>AI-Powered Matching</strong>
                                <p>Advanced machine learning algorithms for precise candidate-job matching and automated screening</p>
                            </div>
                        </div>
                        <div class="feature-item">
                            <i class="bi bi-graph-up-arrow"></i>
                            <div>
                                <strong>Advanced Analytics</strong>
                                <p>Comprehensive recruitment insights, performance metrics, and predictive analytics</p>
                            </div>
                        </div>
                        <div class="feature-item">
                            <i class="bi bi-shield-check"></i>
                            <div>
                                <strong>Enterprise Security</strong>
                                <p>Bank-grade security with end-to-end encryption for all your recruitment data</p>
                            </div>
                        </div>
                        <div class="feature-item">
                            <i class="bi bi-lightning-charge"></i>
                            <div>
                                <strong>Lightning Fast</strong>
                                <p>Streamlined workflows with automated processes to accelerate your hiring pipeline</p>
                            </div>
                        </div>
                        <div class="feature-item">
                            <i class="bi bi-people"></i>
                            <div>
                                <strong>Candidate Experience</strong>
                                <p>Modern, intuitive interface that enhances candidate engagement and application rates</p>
                            </div>
                        </div>
                        <div class="feature-item">
                            <i class="bi bi-globe"></i>
                            <div>
                                <strong>Global Reach</strong>
                                <p>Multi-language support and global job board integrations for worldwide talent sourcing</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Enhanced Right Side - Login Form -->
            <div class="login-form-container">
                <div class="login-card">
                    <div class="login-header">
                        <h2>Welcome Back</h2>
                        <p>Sign in to access your AI-powered recruitment dashboard</p>
                    </div>

                    <form id="login-form" class="login-form" novalidate>
                        <div class="form-group">
                            <label for="email" class="form-label">
                                <i class="bi bi-envelope me-2"></i>Email Address
                            </label>
                            <input type="email" id="email" class="form-control" placeholder="Enter your email address" required autocomplete="email">
                            <div class="invalid-feedback">Please enter a valid email address.</div>
                        </div>

                        <div class="form-group">
                            <label for="password" class="form-label">
                                <i class="bi bi-lock me-2"></i>Password
                            </label>
                            <div class="password-input-group">
                                <input type="password" id="password" class="form-control" placeholder="Enter your password" required autocomplete="current-password">
                                <button type="button" class="password-toggle" onclick="togglePassword()" aria-label="Toggle password visibility">
                                    <i class="bi bi-eye" id="password-icon"></i>
                                </button>
                            </div>
                            <div class="invalid-feedback">Password is required.</div>
                        </div>

                        <div class="form-options">
                            <div class="remember-section">
                                <input type="checkbox" id="remember" class="form-check-input">
                                <label for="remember" class="form-check-label">Remember me for 30 days</label>
                            </div>
                            <a href="#" class="forgot-link" onclick="showForgotPassword()">Forgot Password?</a>
                        </div>

                        <button type="submit" class="btn-login" id="login-btn">
                            <span class="btn-content">
                                <span class="btn-text">
                                    <i class="bi bi-box-arrow-in-right me-2"></i>
                                    Sign In to Dashboard
                                </span>
                                <span class="btn-loading d-none">
                                    <i class="spinner-border spinner-border-sm me-2"></i>
                                    Authenticating...
                                </span>
                            </span>
                        </button>

                        <!-- Enhanced Demo Credentials -->
                        <div class="demo-credentials">
                            <p class="demo-title">Demo Credentials:</p>
                            <div class="demo-info">
                                <span><strong>Email:</strong> <EMAIL></span>
                                <span><strong>Password:</strong> password123</span>
                            </div>
                        </div>
                    </form>

                    <div class="login-footer">
                        <p>Don't have an account? <a href="#" onclick="showContactAdmin()">Contact Administrator</a></p>
                        <div class="powered-by">
                            <small>Powered by Cube AI Solutions © 2024 | <a href="#" class="text-muted">Privacy Policy</a> | <a href="#" class="text-muted">Terms of Service</a></small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Enhanced Main Application -->
    <div id="main-app" class="main-application d-none">
        <!-- Enhanced Top Header -->
        <header class="top-header">
            <div class="header-content">
                <!-- Left Section -->
                <div class="header-left">
                    <div class="app-brand">
                        <i class="bi bi-cube"></i>
                        <span class="brand-text">Cube AI Recruit</span>
                    </div>
                    <div class="breadcrumb-nav">
                        <i class="bi bi-house-door me-2"></i>
                        <span id="current-module">Dashboard</span>
                    </div>
                </div>
                
                <!-- Enhanced Center Section -->
                <div class="header-center">
                    <div class="search-container">
                        <div class="search-input-group">
                            <i class="bi bi-search search-icon"></i>
                            <input type="text" placeholder="Search candidates, jobs, contacts..." class="global-search" id="global-search">
                        </div>
                    </div>
                </div>

                <!-- Enhanced Right Section -->
                <div class="header-right">
                    <div class="header-actions">
                        <!-- Quick Actions -->
                        <div class="quick-actions d-flex gap-2">
                            <button class="action-btn" title="Add Candidate" onclick="showAddCandidateModal()">
                                <i class="bi bi-person-plus"></i>
                            </button>
                            <button class="action-btn" title="Post Job" onclick="showAddJobModal()">
                                <i class="bi bi-briefcase-fill"></i>
                            </button>
                            <button class="action-btn" title="Schedule Interview" onclick="showScheduleModal()">
                                <i class="bi bi-calendar-plus"></i>
                            </button>
                        </div>

                        <!-- Enhanced Notifications -->
                        <div class="notifications dropdown">
                            <button class="notification-btn" data-bs-toggle="dropdown" aria-expanded="false" title="Notifications">
                                <i class="bi bi-bell"></i>
                                <span class="notification-badge" id="notification-badge">5</span>
                            </button>
                            <div class="dropdown-menu dropdown-menu-end p-0" style="width: 380px;">
                                <div class="p-3 border-bottom">
                                    <h6 class="mb-1"><i class="bi bi-bell me-2"></i>Notifications</h6>
                                    <button class="btn btn-sm btn-link p-0" onclick="markAllNotificationsRead()">Mark all as read</button>
                                </div>
                                <div class="notification-list" id="notification-list" style="max-height: 400px; overflow-y: auto;">
                                    <!-- Enhanced notification items -->
                                    <div class="p-3 border-bottom">
                                        <div class="d-flex align-items-start">
                                            <img src="https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=32&h=32&fit=crop&crop=face" class="rounded-circle me-3" width="32" height="32" alt="User">
                                            <div class="flex-grow-1">
                                                <h6 class="mb-1 fs-6">New candidate application</h6>
                                                <p class="mb-1 text-muted small">Arun Prakash applied for Senior Developer position</p>
                                                <small class="text-muted">2 minutes ago</small>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="p-3 border-bottom">
                                        <div class="d-flex align-items-start">
                                            <div class="bg-success rounded-circle me-3 d-flex align-items-center justify-content-center" style="width: 32px; height: 32px;">
                                                <i class="bi bi-check text-white"></i>
                                            </div>
                                            <div class="flex-grow-1">
                                                <h6 class="mb-1 fs-6">Interview scheduled</h6>
                                                <p class="mb-1 text-muted small">Technical interview with Keerthana Suresh tomorrow at 2 PM</p>
                                                <small class="text-muted">1 hour ago</small>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="p-3 border-bottom">
                                        <div class="d-flex align-items-start">
                                            <div class="bg-warning rounded-circle me-3 d-flex align-items-center justify-content-center" style="width: 32px; height: 32px;">
                                                <i class="bi bi-exclamation text-white"></i>
                                            </div>
                                            <div class="flex-grow-1">
                                                <h6 class="mb-1 fs-6">Candidate follow-up required</h6>
                                                <p class="mb-1 text-muted small">3 candidates awaiting response for Product Manager role</p>
                                                <small class="text-muted">3 hours ago</small>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="p-3 text-center border-top">
                                    <a href="#" class="btn btn-sm btn-outline-primary">View all notifications</a>
                                </div>
                            </div>
                        </div>

                        <!-- Enhanced User Menu -->
                        <div class="user-menu dropdown">
                            <button class="user-btn" data-bs-toggle="dropdown" aria-expanded="false">
                                <img src="https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=32&h=32&fit=crop&crop=face" 
                                     alt="User Avatar" class="user-avatar">
                                <div class="user-info">
                                    <span class="user-name fw-semibold">Admin User</span>
                                    <small class="user-role text-muted">System Administrator</small>
                                </div>
                                <i class="bi bi-chevron-down"></i>
                            </button>
                            <div class="dropdown-menu dropdown-menu-end" style="width: 300px;">
                                <div class="p-3 border-bottom">
                                    <div class="d-flex align-items-center">
                                        <img src="https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=48&h=48&fit=crop&crop=face" 
                                             alt="User Avatar" class="rounded-circle me-3" width="48" height="48">
                                        <div>
                                            <h6 class="mb-0">Admin User</h6>
                                            <p class="mb-1 text-muted small"><EMAIL></p>
                                            <span class="badge bg-success-subtle text-success">Online</span>
                                        </div>
                                    </div>
                                </div>
                                <a class="dropdown-item py-2" href="#"><i class="bi bi-person me-2"></i>My Profile</a>
                                <a class="dropdown-item py-2" href="#"><i class="bi bi-gear me-2"></i>Account Settings</a>
                                <a class="dropdown-item py-2" href="#"><i class="bi bi-bell me-2"></i>Notification Preferences</a>
                                <a class="dropdown-item py-2" href="#"><i class="bi bi-shield-check me-2"></i>Security Settings</a>
                                <a class="dropdown-item py-2" href="#"><i class="bi bi-question-circle me-2"></i>Help & Support</a>
                                <div class="dropdown-divider"></div>
                                <a class="dropdown-item py-2 text-danger" href="#" onclick="logout()">
                                    <i class="bi bi-box-arrow-right me-2"></i>Sign Out
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </header>

        <!-- Enhanced Main Navigation -->
        <nav class="main-navigation">
            <div class="nav-container">
                <div class="nav-tabs-wrapper">
                    <button class="nav-tab active" data-module="dashboard">
                        <i class="bi bi-house-door"></i>
                        <span>Dashboard</span>
                    </button>
                    <a class="nav-tab" href="canditates.html" style="text-decoration:none;">
                        <i class="bi bi-people"></i>
                        <span>Candidates</span>
                        <span class="tab-badge">186</span>
                    </a>
                    <button class="nav-tab" data-module="jobs">
                        <i class="bi bi-briefcase"></i>
                        <span>Job Openings</span>
                        <span class="tab-badge">28</span>
                    </button>
                    <button class="nav-tab" data-module="contacts">
                        <i class="bi bi-person-lines-fill"></i>
                        <span>Contacts</span>
                    </button>
                    <button class="nav-tab" data-module="interviews">
                        <i class="bi bi-calendar-check"></i>
                        <span>Interviews</span>
                        <span class="tab-badge">52</span>
                    </button>
                    <button class="nav-tab" data-module="analytics">
                        <i class="bi bi-graph-up"></i>
                        <span>Analytics</span>
                    </button>
                    <button class="nav-tab" data-module="reports">
                        <i class="bi bi-file-earmark-bar-graph"></i>
                        <span>Reports</span>
                    </button>
                    <button class="nav-tab" data-module="setup">
                        <i class="bi bi-gear"></i>
                        <span>Setup</span>
                    </button>
                </div>
                
                <div class="nav-actions d-flex gap-2">
                    <button class="btn btn-primary btn-sm" onclick="showQuickAddMenu()">
                        <i class="bi bi-plus-lg me-1"></i>Add
                    </button>
                    <button class="btn btn-outline-secondary btn-sm" onclick="showImportModal()">
                        <i class="bi bi-upload"></i>
                    </button>
                    <button class="btn btn-outline-secondary btn-sm" onclick="showExportModal()">
                        <i class="bi bi-download"></i>
                    </button>
                </div>
            </div>
        </nav>

        <!-- Enhanced Main Content Area -->
        <main class="main-content">
            <!-- Enhanced Dashboard Module -->
            <div id="dashboard-module" class="module-content active">
                <!-- Enhanced Dashboard Header -->
                <div class="module-header">
                    <div class="header-info">
                        <h1><i class="bi bi-speedometer2 me-3"></i>Dashboard</h1>
                        <p>AI-powered recruitment insights and command center</p>
                    </div>
                    <div class="header-actions d-flex gap-2">
                        <button class="btn btn-outline-primary" onclick="customizeDashboard()">
                            <i class="bi bi-gear me-2"></i>Customize Dashboard
                        </button>
                        <button class="btn btn-primary" onclick="showQuickAddMenu()">
                            <i class="bi bi-plus me-2"></i>Quick Add
                        </button>
                    </div>
                </div>

                <!-- Enhanced Dashboard Grid -->
                <div class="dashboard-grid">
                    <!-- Enhanced Overview Stats -->
                    <div class="widget stats-widget">
                        <div class="widget-header">
                            <h3><i class="bi bi-bar-chart me-2"></i>Recruitment Overview</h3>
                            <div class="dropdown">
                                <button class="btn btn-sm btn-outline-secondary" data-bs-toggle="dropdown">
                                    <i class="bi bi-three-dots"></i>
                                </button>
                                <ul class="dropdown-menu">
                                    <li><a class="dropdown-item" href="#">Refresh Data</a></li>
                                    <li><a class="dropdown-item" href="#">Export Report</a></li>
                                    <li><a class="dropdown-item" href="#">Customize View</a></li>
                                </ul>
                            </div>
                        </div>
                        <div class="widget-content">
                            <div class="stats-grid">
                                <div class="stat-card primary">
                                    <div class="stat-icon">
                                        <i class="bi bi-briefcase"></i>
                                    </div>
                                    <div class="stat-details">
                                        <h4 class="stat-number" data-target="28">0</h4>
                                        <p class="stat-label">Active Jobs</p>
                                        <span class="stat-trend positive">
                                            <i class="bi bi-arrow-up"></i>+4 this week
                                        </span>
                                    </div>
                                </div>
                                <div class="stat-card success">
                                    <div class="stat-icon">
                                        <i class="bi bi-people"></i>
                                    </div>
                                    <div class="stat-details">
                                        <h4 class="stat-number" data-target="186">0</h4>
                                        <p class="stat-label">Total Candidates</p>
                                        <span class="stat-trend positive">
                                            <i class="bi bi-arrow-up"></i>+34 this month
                                        </span>
                                    </div>
                                </div>
                                <div class="stat-card warning">
                                    <div class="stat-icon">
                                        <i class="bi bi-calendar-check"></i>
                                    </div>
                                    <div class="stat-details">
                                        <h4 class="stat-number" data-target="52">0</h4>
                                        <p class="stat-label">Interviews Scheduled</p>
                                        <span class="stat-trend positive">
                                            <i class="bi bi-arrow-up"></i>+15 this week
                                        </span>
                                    </div>
                                </div>
                                <div class="stat-card info">
                                    <div class="stat-icon">
                                        <i class="bi bi-person-check"></i>
                                    </div>
                                    <div class="stat-details">
                                        <h4 class="stat-number" data-target="12">0</h4>
                                        <p class="stat-label">Offers Extended</p>
                                        <span class="stat-trend positive">
                                            <i class="bi bi-arrow-up"></i>+3 this week
                                        </span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Enhanced Recent Activities -->
                    <div class="widget activities-widget">
                        <div class="widget-header">
                            <h3><i class="bi bi-clock-history me-2"></i>Recent Activities</h3>
                            <a href="#" class="btn btn-sm btn-outline-primary">View All</a>
                        </div>
                        <div class="widget-content">
                            <div class="activity-timeline">
                                <div class="activity-item">
                                    <div class="activity-avatar">
                                        <img src="https://images.unsplash.com/photo-1494790108755-2616b612b287?w=40&h=40&fit=crop&crop=face" alt="Candidate">
                                        <div class="status-indicator online"></div>
                                    </div>
                                    <div class="activity-info flex-grow-1">
                                        <h6 class="mb-1">Arun Prakash applied for Senior Developer</h6>
                                        <p class="mb-1 text-muted small">Strong background in React and Node.js. Previous experience at tech startups.</p>
                                        <small class="text-muted">2 minutes ago</small>
                                    </div>
                                    <div class="activity-action">
                                        <button class="btn btn-sm btn-outline-primary">Review</button>
                                        <button class="btn btn-sm btn-outline-success">Accept</button>
                                    </div>
                                </div>
                                <div class="activity-item">
                                    <div class="activity-avatar">
                                        <img src="https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=40&h=40&fit=crop&crop=face" alt="Recruiter">
                                        <div class="status-indicator online"></div>
                                    </div>
                                    <div class="activity-info flex-grow-1">
                                        <h6 class="mb-1">Keerthana Suresh scheduled interview</h6>
                                        <p class="mb-1 text-muted small">Technical interview for Product Manager position on Thursday 2 PM.</p>
                                        <small class="text-muted">1 hour ago</small>
                                    </div>
                                    <div class="activity-action">
                                        <button class="btn btn-sm btn-outline-primary">View</button>
                                    </div>
                                </div>
                                <div class="activity-item">
                                    <div class="activity-avatar">
                                        <img src="https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=40&h=40&fit=crop&crop=face" alt="HR">
                                        <div class="status-indicator online"></div>
                                    </div>
                                    <div class="activity-info flex-grow-1">
                                        <h6 class="mb-1">Meena Ramesh posted new job</h6>
                                        <p class="mb-1 text-muted small">UX Designer position with competitive salary and remote work options.</p>
                                        <small class="text-muted">3 hours ago</small>
                                    </div>
                                    <div class="activity-action">
                                        <button class="btn btn-sm btn-outline-primary">View Job</button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Enhanced Hiring Pipeline -->
                    <div class="widget pipeline-widget">
                        <div class="widget-header">
                            <h3><i class="bi bi-funnel me-2"></i>Hiring Pipeline</h3>
                            <select class="form-select form-select-sm" style="width: auto;">
                                <option>This Month</option>
                                <option>Last Month</option>
                                <option>This Quarter</option>
                                <option>Last Quarter</option>
                            </select>
                        </div>
                        <div class="widget-content">
                            <div class="pipeline-chart">
                                <div class="pipeline-stage">
                                    <div class="stage-info">
                                        <h6 class="mb-0">Applied</h6>
                                        <span class="stage-count">58</span>
                                    </div>
                                    <div class="stage-bar">
                                        <div class="progress-fill" data-percentage="100"></div>
                                    </div>
                                </div>
                                <div class="pipeline-stage">
                                    <div class="stage-info">
                                        <h6 class="mb-0">Screening</h6>
                                        <span class="stage-count">34</span>
                                    </div>
                                    <div class="stage-bar">
                                        <div class="progress-fill" data-percentage="59"></div>
                                    </div>
                                </div>
                                <div class="pipeline-stage">
                                    <div class="stage-info">
                                        <h6 class="mb-0">Interview</h6>
                                        <span class="stage-count">18</span>
                                    </div>
                                    <div class="stage-bar">
                                        <div class="progress-fill" data-percentage="31"></div>
                                    </div>
                                </div>
                                <div class="pipeline-stage">
                                    <div class="stage-info">
                                        <h6 class="mb-0">Final Round</h6>
                                        <span class="stage-count">9</span>
                                    </div>
                                    <div class="stage-bar">
                                        <div class="progress-fill" data-percentage="16"></div>
                                    </div>
                                </div>
                                <div class="pipeline-stage">
                                    <div class="stage-info">
                                        <h6 class="mb-0">Offer</h6>
                                        <span class="stage-count">4</span>
                                    </div>
                                    <div class="stage-bar">
                                        <div class="progress-fill" data-percentage="7"></div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Enhanced Quick Actions -->
                    <div class="widget quick-actions-widget">
                        <div class="widget-header">
                            <h3><i class="bi bi-lightning-charge me-2"></i>Quick Actions</h3>
                        </div>
                        <div class="widget-content">
                            <div class="quick-actions-grid">
                                <button class="quick-action-card" onclick="switchModule('candidates')">
                                    <div class="action-icon primary">
                                        <i class="bi bi-person-plus"></i>
                                    </div>
                                    <span>Add Candidate</span>
                                </button>
                                <button class="quick-action-card" onclick="switchModule('jobs')">
                                    <div class="action-icon success">
                                        <i class="bi bi-briefcase"></i>
                                    </div>
                                    <span>Post New Job</span>
                                </button>
                                <button class="quick-action-card" onclick="switchModule('interviews')">
                                    <div class="action-icon warning">
                                        <i class="bi bi-calendar-plus"></i>
                                    </div>
                                    <span>Schedule Interview</span>
                                </button>
                                <button class="quick-action-card" onclick="switchModule('reports')">
                                    <div class="action-icon info">
                                        <i class="bi bi-graph-up"></i>
                                    </div>
                                    <span>Generate Report</span>
                                </button>
                                <button class="quick-action-card" onclick="showBulkImport()">
                                    <div class="action-icon secondary">
                                        <i class="bi bi-upload"></i>
                                    </div>
                                    <span>Import Data</span>
                                </button>
                                <button class="quick-action-card" onclick="switchModule('analytics')">
                                    <div class="action-icon dark">
                                        <i class="bi bi-bar-chart"></i>
                                    </div>
                                    <span>View Analytics</span>
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- Enhanced AI Insights Widget -->
                    <div class="widget ai-insights-widget">
                        <div class="widget-header">
                            <h3><i class="bi bi-robot me-2"></i>AI Insights</h3>
                            <span class="badge bg-primary-subtle text-primary">New</span>
                        </div>
                        <div class="widget-content">
                            <div class="d-flex flex-column gap-3">
                                <div class="p-3 border rounded-3 bg-light">
                                    <div class="d-flex align-items-center mb-2">
                                        <i class="bi bi-lightbulb text-warning me-2"></i>
                                        <strong class="text-dark">Recommendation</strong>
                                    </div>
                                    <p class="mb-2 text-muted small">3 candidates in your pipeline are perfect matches for the Senior Developer role. Consider fast-tracking their interviews.</p>
                                    <button class="btn btn-sm btn-outline-primary">View Matches</button>
                                </div>
                                <div class="p-3 border rounded-3 bg-light">
                                    <div class="d-flex align-items-center mb-2">
                                        <i class="bi bi-graph-up text-success me-2"></i>
                                        <strong class="text-dark">Trend Alert</strong>
                                    </div>
                                    <p class="mb-2 text-muted small">Your response time has improved by 40% this month. Candidates are more likely to accept offers!</p>
                                </div>
                                <div class="p-3 border rounded-3 bg-light">
                                    <div class="d-flex align-items-center mb-2">
                                        <i class="bi bi-clock text-info me-2"></i>
                                        <strong class="text-dark">Optimization</strong>
                                    </div>
                                    <p class="mb-2 text-muted small">Thursday 2-4 PM shows highest candidate engagement. Schedule important interviews during this time.</p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Enhanced Top Performers -->
                    <div class="widget performers-widget">
                        <div class="widget-header">
                            <h3><i class="bi bi-star me-2"></i>Top Performers</h3>
                            <a href="#" class="btn btn-sm btn-outline-primary">View All</a>
                        </div>
                        <div class="widget-content">
                            <div class="d-flex flex-column gap-3">
                                <div class="d-flex align-items-center">
                                    <div class="position-relative me-3">
                                        <img src="https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=48&h=48&fit=crop&crop=face" class="rounded-circle" width="48" height="48" alt="Recruiter">
                                        <div class="status-indicator online"></div>
                                    </div>
                                    <div class="flex-grow-1">
                                        <h6 class="mb-1">Arun Prakash</h6>
                                        <p class="mb-1 text-muted small">Senior Recruiter</p>
                                        <div class="d-flex gap-3 small text-muted">
                                            <span><i class="bi bi-person-check text-primary"></i> 15 Hires</span>
                                            <span><i class="bi bi-bullseye text-success"></i> 87% Success</span>
                                        </div>
                                    </div>
                                    <div class="text-center">
                                        <div class="rounded-circle bg-primary text-white d-flex align-items-center justify-content-center fw-bold" style="width: 48px; height: 48px;">
                                            97
                                        </div>
                                        <small class="text-muted">Score</small>
                                    </div>
                                </div>
                                <div class="d-flex align-items-center">
                                    <div class="position-relative me-3">
                                        <img src="https://images.unsplash.com/photo-1494790108755-2616b612b287?w=48&h=48&fit=crop&crop=face" class="rounded-circle" width="48" height="48" alt="Recruiter">
                                        <div class="status-indicator online"></div>
                                    </div>
                                    <div class="flex-grow-1">
                                        <h6 class="mb-1">Keerthana Suresh</h6>
                                        <p class="mb-1 text-muted small">Technical Recruiter</p>
                                        <div class="d-flex gap-3 small text-muted">
                                            <span><i class="bi bi-person-check text-primary"></i> 12 Hires</span>
                                            <span><i class="bi bi-bullseye text-success"></i> 94% Success</span>
                                        </div>
                                    </div>
                                    <div class="text-center">
                                        <div class="rounded-circle bg-success text-white d-flex align-items-center justify-content-center fw-bold" style="width: 48px; height: 48px;">
                                            92
                                        </div>
                                        <small class="text-muted">Score</small>
                                    </div>
                                </div>
                                <div class="d-flex align-items-center">
                                    <div class="position-relative me-3">
                                        <img src="https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=48&h=48&fit=crop&crop=face" class="rounded-circle" width="48" height="48" alt="Recruiter">
                                        <div class="status-indicator online"></div>
                                    </div>
                                    <div class="flex-grow-1">
                                        <h6 class="mb-1">Meena Ramesh</h6>
                                        <p class="mb-1 text-muted small">HR Specialist</p>
                                        <div class="d-flex gap-3 small text-muted">
                                            <span><i class="bi bi-person-check text-primary"></i> 9 Hires</span>
                                            <span><i class="bi bi-bullseye text-success"></i> 89% Success</span>
                                        </div>
                                    </div>
                                    <div class="text-center">
                                        <div class="rounded-circle bg-warning text-white d-flex align-items-center justify-content-center fw-bold" style="width: 48px; height: 48px;">
                                            85
                                        </div>
                                        <small class="text-muted">Score</small>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Google Drive Integration Widget -->
                    <div class="widget integration-widget">
                        <div class="widget-header">
                            <h3><i class="bi bi-google me-2"></i>Google Drive Integration</h3>
                            <div class="dropdown">
                                <button class="btn btn-sm btn-outline-secondary" data-bs-toggle="dropdown">
                                    <i class="bi bi-three-dots"></i>
                                </button>
                                <ul class="dropdown-menu">
                                    <li><a class="dropdown-item" href="#" onclick="refreshGoogleDriveStatus()">Refresh Status</a></li>
                                    <li><a class="dropdown-item" href="#" onclick="configureGoogleDrive()">Configure</a></li>
                                    <li><a class="dropdown-item" href="#" onclick="disconnectGoogleDrive()">Disconnect</a></li>
                                </ul>
                            </div>
                        </div>
                        <div class="widget-content">
                            <div class="integration-status mb-3">
                                <div class="d-flex align-items-center justify-content-between">
                                    <div class="d-flex align-items-center">
                                        <div class="status-indicator" id="googleDriveStatus">
                                            <i class="bi bi-circle-fill text-danger"></i>
                                        </div>
                                        <span class="ms-2" id="googleDriveStatusText">Disconnected</span>
                                    </div>
                                    <button class="btn btn-sm btn-primary" id="connectGoogleDriveBtn" onclick="connectToGoogleDrive()">
                                        <i class="bi bi-link me-1"></i>Connect
                                    </button>
                                </div>
                            </div>

                            <div id="googleDriveConfig" style="display: none;">
                                <div class="row g-3 mb-3">
                                    <div class="col-md-6">
                                        <label class="form-label">Folder ID</label>
                                        <input type="text" class="form-control" id="googleDriveFolderId" placeholder="Enter Google Drive folder ID">
                                        <small class="text-muted">Folder where resumes are stored</small>
                                    </div>
                                    <div class="col-md-6">
                                        <label class="form-label">Sync Frequency</label>
                                        <select class="form-select" id="googleDriveSyncFreq">
                                            <option value="manual">Manual</option>
                                            <option value="hourly">Every Hour</option>
                                            <option value="daily">Daily</option>
                                            <option value="weekly">Weekly</option>
                                        </select>
                                    </div>
                                </div>

                                <div class="form-check mb-3">
                                    <input class="form-check-input" type="checkbox" id="autoProcessResumes" checked>
                                    <label class="form-check-label" for="autoProcessResumes">
                                        Auto-process new resumes with AI
                                    </label>
                                </div>

                                <div class="d-grid gap-2">
                                    <button class="btn btn-success" onclick="testGoogleDriveConnection()">
                                        <i class="bi bi-wifi me-2"></i>Test Connection
                                    </button>
                                    <button class="btn btn-info" onclick="syncFromGoogleDrive()">
                                        <i class="bi bi-download me-2"></i>Sync Resumes Now
                                    </button>
                                </div>

                                <div class="mt-3">
                                    <small class="text-muted">
                                        <i class="bi bi-info-circle me-1"></i>
                                        AI will automatically analyze and score new resumes from Google Drive
                                    </small>
                                </div>
                            </div>

                            <div id="googleDriveStats" class="mt-3" style="display: none;">
                                <div class="row text-center">
                                    <div class="col-4">
                                        <div class="stat-mini">
                                            <h6 class="mb-0" id="totalDriveFiles">0</h6>
                                            <small class="text-muted">Files Found</small>
                                        </div>
                                    </div>
                                    <div class="col-4">
                                        <div class="stat-mini">
                                            <h6 class="mb-0" id="processedDriveFiles">0</h6>
                                            <small class="text-muted">Processed</small>
                                        </div>
                                    </div>
                                    <div class="col-4">
                                        <div class="stat-mini">
                                            <h6 class="mb-0" id="lastDriveSync">Never</h6>
                                            <small class="text-muted">Last Sync</small>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Enhanced Placeholder Modules -->
            <div id="candidates-module" class="module-content">
                <div class="module-header">
                    <div class="header-info">
                        <h1><i class="bi bi-people me-3"></i>Candidates</h1>
                        <p>Comprehensive candidate management and AI-powered matching</p>
                    </div>
                    <div class="header-actions d-flex gap-2">
                        <button class="btn btn-outline-primary">
                            <i class="bi bi-funnel me-2"></i>Advanced Filters
                        </button>
                        <button class="btn btn-primary">
                            <i class="bi bi-person-plus me-2"></i>Add Candidate
                        </button>
                    </div>
                </div>                <!-- In your main navigation, update the Candidates nav-tab to link to canditates.html -->
                
                <nav class="main-navigation">
                    <div class="nav-container">
                        <div class="nav-tabs-wrapper">
                            <button class="nav-tab active" data-module="dashboard">
                                <i class="bi bi-house-door"></i>
                                <span>Dashboard</span>
                            </button>
                            <a class="nav-tab" href="canditates.html" style="text-decoration:none;">
                                <i class="bi bi-people"></i>
                                <span>Candidates</span>
                                <span class="tab-badge">186</span>
                            </a>
                            <button class="nav-tab" data-module="jobs">
                                <i class="bi bi-briefcase"></i>
                                <span>Job Openings</span>
                                <span class="tab-badge">28</span>
                            </button>
                            <button class="nav-tab" data-module="contacts">
                                <i class="bi bi-person-lines-fill"></i>
                                <span>Contacts</span>
                            </button>
                            <button class="nav-tab" data-module="interviews">
                                <i class="bi bi-calendar-check"></i>
                                <span>Interviews</span>
                                <span class="tab-badge">52</span>
                            </button>
                            <button class="nav-tab" data-module="analytics">
                                <i class="bi bi-graph-up"></i>
                                <span>Analytics</span>
                            </button>
                            <button class="nav-tab" data-module="reports">
                                <i class="bi bi-file-earmark-bar-graph"></i>
                                <span>Reports</span>
                            </button>
                            <button class="nav-tab" data-module="setup">
                                <i class="bi bi-gear"></i>
                                <span>Setup</span>
                            </button>
                        </div>
                        <!-- ...nav-actions... -->
                    </div>
                </nav>
                <div class="coming-soon">
                    <i class="bi bi-people display-1"></i>
                    <h3>Advanced Candidate Management</h3>
                    <p>AI-powered candidate sourcing, screening, and management system with automated workflows and intelligent matching algorithms.</p>
                    <div class="d-flex gap-2 justify-content-center">
                        <button class="btn btn-primary" onclick="showNotImplementedMessage('Candidates Module')">
                            <i class="bi bi-play me-2"></i>Learn More
                        </button>
                        <button class="btn btn-outline-primary" onclick="showNotImplementedMessage('Candidates Module')">
                            <i class="bi bi-eye me-2"></i>Preview Features
                        </button>
                    </div>
                </div>
            </div>

            <div id="jobs-module" class="module-content">
                <div class="module-header">
                    <div class="header-info">
                        <h1><i class="bi bi-briefcase me-3"></i>Job Openings</h1>
                        <p>Create, manage, and optimize job postings with AI assistance</p>
                    </div>
                    <div class="header-actions d-flex gap-2">
                        <button class="btn btn-outline-primary">
                            <i class="bi bi-eye me-2"></i>View Options
                        </button>
                        <button class="btn btn-primary">
                            <i class="bi bi-plus me-2"></i>Post New Job
                        </button>
                    </div>
                </div>
                <div class="coming-soon">
                    <i class="bi bi-briefcase display-1"></i>
                    <h3>Intelligent Job Management</h3>
                    <p>AI-powered job description optimization, multi-platform posting, and automated candidate matching with performance analytics.</p>
                    <div class="d-flex gap-2 justify-content-center">
                        <button class="btn btn-primary" onclick="showNotImplementedMessage('Job Openings Module')">
                            <i class="bi bi-play me-2"></i>Learn More
                        </button>
                        <button class="btn btn-outline-primary" onclick="showNotImplementedMessage('Job Openings Module')">
                            <i class="bi bi-eye me-2"></i>Preview Features
                        </button>
                    </div>
                </div>
            </div>

            <div id="contacts-module" class="module-content">
                <div class="module-header">
                    <div class="header-info">
                    </div>
                </div>
            </div>

            <div id="interviews-module" class="module-content">
                <div class="module-header">
                    <div class="header-info">
                        <h1><i class="bi bi-calendar-check me-3"></i>Interviews</h1>
                        <p>Streamlined interview scheduling and management system</p>
                    </div>
                    <div class="header-actions d-flex gap-2">
                        <button class="btn btn-primary">
                            <i class="bi bi-calendar-plus me-2"></i>Schedule Interview
                        </button>
                    </div>
                </div>
                <div class="coming-soon">
                    <i class="bi bi-calendar-check display-1"></i>
                    <h3>Advanced Interview Management</h3>
                    <p>AI-powered scheduling, video interview integration, automated scoring, and collaborative evaluation tools.</p>
                    <div class="d-flex gap-2 justify-content-center">
                        <button class="btn btn-primary" onclick="showNotImplementedMessage('Interviews Module')">
                            <i class="bi bi-play me-2"></i>Learn More
                        </button>
                        <button class="btn btn-outline-primary" onclick="showNotImplementedMessage('Interviews Module')">
                            <i class="bi bi-eye me-2"></i>Preview Features
                        </button>
                    </div>
                </div>
            </div>

            <div id="analytics-module" class="module-content">
                <div class="module-header">
                    <div class="header-info">
                        <h1><i class="bi bi-graph-up me-3"></i>Analytics</h1>
                        <p>Advanced recruitment analytics and performance insights</p>
                    </div>
                </div>
                <div class="coming-soon">
                    <i class="bi bi-graph-up display-1"></i>
                    <h3>Predictive Analytics Dashboard</h3>
                    <p>Machine learning-powered insights, predictive modeling, performance benchmarking, and ROI analysis for data-driven decisions.</p>
                    <div class="d-flex gap-2 justify-content-center">
                        <button class="btn btn-primary" onclick="showNotImplementedMessage('Analytics Module')">
                            <i class="bi bi-play me-2"></i>Learn More
                        </button>
                        <button class="btn btn-outline-primary" onclick="showNotImplementedMessage('Analytics Module')">
                            <i class="bi bi-eye me-2"></i>Preview Features
                        </button>
                    </div>
                </div>
            </div>

            <div id="reports-module" class="module-content">
                <div class="module-header">
                    <div class="header-info">
                        <h1><i class="bi bi-file-earmark-bar-graph me-3"></i>Reports</h1>
                        <p>Comprehensive reporting and data visualization suite</p>
                    </div>
                    <div class="header-actions d-flex gap-2">
                        <button class="btn btn-primary">
                            <i class="bi bi-plus me-2"></i>Create Report
                        </button>
                    </div>
                </div>
                <div class="coming-soon">
                    <i class="bi bi-file-earmark-bar-graph display-1"></i>
                    <h3>Dynamic Reporting Engine</h3>
                    <p>Custom report builder, automated scheduling, interactive dashboards, and executive summaries with export capabilities.</p>
                    <div class="d-flex gap-2 justify-content-center">
                        <button class="btn btn-primary" onclick="showNotImplementedMessage('Reports Module')">
                            <i class="bi bi-play me-2"></i>Learn More
                        </button>
                        <button class="btn btn-outline-primary" onclick="showNotImplementedMessage('Reports Module')">
                            <i class="bi bi-eye me-2"></i>Preview Features
                        </button>
                    </div>
                </div>
            </div>

            <div id="setup-module" class="module-content">
                <div class="module-header">
                    <div class="header-info">
                        <h1><i class="bi bi-gear me-3"></i>Setup</h1>
                        <p>System configuration and administrative settings</p>
                    </div>
                </div>
                <div class="coming-soon">
                    <i class="bi bi-gear display-1"></i>
                    <h3>Administrative Control Center</h3>
                    <p>User management, role-based permissions, integration settings, workflow automation, and system configuration tools.</p>
                    <div class="d-flex gap-2 justify-content-center">
                        <button class="btn btn-primary" onclick="showNotImplementedMessage('Setup Module')">
                            <i class="bi bi-play me-2"></i>Learn More
                        </button>
                        <button class="btn btn-outline-primary" onclick="showNotImplementedMessage('Setup Module')">
                            <i class="bi bi-eye me-2"></i>Preview Features
                        </button>
                    </div>
                </div>
            </div>
        </main>
    </div>

    <!-- Enhanced JavaScript -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Enhanced Application State
        let currentUser = null;
        let isAuthenticated = false;

        // Enhanced Initialization
        document.addEventListener('DOMContentLoaded', function() {
            // Show loading screen
            showLoadingScreen();
            
            // Initialize application after delay
            setTimeout(() => {
                hideLoadingScreen();
            }, 2000);
            
            // Initialize event listeners
            initializeEventListeners();
        });

        function initializeEventListeners() {
            // Login form submission
            const loginForm = document.getElementById('login-form');
            if (loginForm) {
                loginForm.addEventListener('submit', handleLogin);
            }

            // Navigation tabs
            const navTabs = document.querySelectorAll('.nav-tab');
            navTabs.forEach(tab => {
                tab.addEventListener('click', function() {
                    const module = this.getAttribute('data-module');
                    switchModule(module);
                });
            });

            // Global search
            const globalSearch = document.getElementById('global-search');
            if (globalSearch) {
                globalSearch.addEventListener('input', handleGlobalSearch);
            }

            // Animate stats on dashboard load
            animateStats();
            
            // Initialize pipeline progress bars
            initializePipelineProgress();
        }

        // Enhanced Loading Screen Functions
        function showLoadingScreen() {
            const loadingScreen = document.getElementById('loading-screen');
            if (loadingScreen) {
                loadingScreen.classList.remove('fade-out');
            }
        }

        function hideLoadingScreen() {
            const loadingScreen = document.getElementById('loading-screen');
            if (loadingScreen) {
                loadingScreen.classList.add('fade-out');
                setTimeout(() => {
                    loadingScreen.style.display = 'none';
                }, 500);
            }
        }

        // Enhanced Login Functions
        function handleLogin(e) {
            e.preventDefault();
            
            const email = document.getElementById('email').value;
            const password = document.getElementById('password').value;
            const loginBtn = document.getElementById('login-btn');
            const btnText = loginBtn.querySelector('.btn-text');
            const btnLoading = loginBtn.querySelector('.btn-loading');
            
            // Basic validation
            if (!email || !password) {
                showNotification('Please fill in all fields', 'error');
                return;
            }
            
            // Show loading state
            btnText.classList.add('d-none');
            btnLoading.classList.remove('d-none');
            loginBtn.disabled = true;
            
            // Simulate authentication
            setTimeout(() => {
                if (email === '<EMAIL>' && password === 'password123') {
                    // Success
                    currentUser = {
                        name: 'Admin User',
                        email: email,
                        role: 'System Administrator'
                    };
                    isAuthenticated = true;
                    
                    showNotification('Login successful! Welcome back.', 'success');
                    
                    // Transition to main app
                    setTimeout(() => {
                        document.getElementById('login-page').classList.add('d-none');
                        document.getElementById('main-app').classList.remove('d-none');
                        
                        // Initialize dashboard
                        initializeDashboard();
                    }, 1000);
                } else {
                    // Failure
                    showNotification('Invalid credentials. Please try again.', 'error');
                    
                    // Reset form state
                    btnText.classList.remove('d-none');
                    btnLoading.classList.add('d-none');
                    loginBtn.disabled = false;
                }
            }, 2000);
        }

        function togglePassword() {
            const passwordInput = document.getElementById('password');
            const passwordIcon = document.getElementById('password-icon');
            
            if (passwordInput.type === 'password') {
                passwordInput.type = 'text';
                passwordIcon.classList.remove('bi-eye');
                passwordIcon.classList.add('bi-eye-slash');
            } else {
                passwordInput.type = 'password';
                passwordIcon.classList.remove('bi-eye-slash');
                passwordIcon.classList.add('bi-eye');
            }
        }

        // Enhanced Dashboard Functions
        function initializeDashboard() {
            animateStats();
            initializePipelineProgress();
            loadRecentActivities();
        }

        function animateStats() {
            const statNumbers = document.querySelectorAll('.stat-number');
            
            statNumbers.forEach(stat => {
                const target = parseInt(stat.getAttribute('data-target'));
                let current = 0;
                const increment = target / 50;
                
                const timer = setInterval(() => {
                    current += increment;
                    if (current >= target) {
                        current = target;
                        clearInterval(timer);
                    }
                    stat.textContent = Math.floor(current);
                }, 50);
            });
        }

        function initializePipelineProgress() {
            const progressBars = document.querySelectorAll('.progress-fill');
            
            setTimeout(() => {
                progressBars.forEach(bar => {
                    const percentage = bar.getAttribute('data-percentage');
                    bar.style.width = percentage + '%';
                });
            }, 1000);
        }

        // Enhanced Navigation Functions
        function switchModule(moduleName) {
            // Update active nav tab
            const navTabs = document.querySelectorAll('.nav-tab');
            navTabs.forEach(tab => {
                tab.classList.remove('active');
                if (tab.getAttribute('data-module') === moduleName) {
                    tab.classList.add('active');
                }
            });
            
            // Update module content
            const moduleContents = document.querySelectorAll('.module-content');
            moduleContents.forEach(content => {
                content.classList.remove('active');
            });
            
            const targetModule = document.getElementById(moduleName + '-module');
            if (targetModule) {
                targetModule.classList.add('active');
            }
            
            // Update breadcrumb
            const currentModuleSpan = document.getElementById('current-module');
            if (currentModuleSpan) {
                currentModuleSpan.textContent = capitalizeFirst(moduleName);
            }
        }

        // Enhanced Search Function
        function handleGlobalSearch(e) {
            const query = e.target.value.toLowerCase();
            
            if (query.length < 2) {
                return;
            }
            
            // Simulate search suggestions
            const suggestions = [
                'Sarah Johnson - Senior Developer',
                'Mike Chen - Product Manager',
                'Software Engineer Position',
                'Interview scheduled for Thursday',
                'Frontend Developer candidates'
            ].filter(item => item.toLowerCase().includes(query));
            
            // You could display these suggestions in a dropdown
            console.log('Search suggestions:', suggestions);
        }

        // Enhanced Utility Functions
        function showNotification(message, type = 'info') {
            // Create notification element
            const notification = document.createElement('div');
            notification.className = `alert alert-${type} alert-dismissible fade show position-fixed`;
            notification.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
            notification.innerHTML = `
                ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            `;
            
            document.body.appendChild(notification);
            
            // Auto remove after 5 seconds
            setTimeout(() => {
                if (notification.parentNode) {
                    notification.remove();
                }
            }, 5000);
        }

        function capitalizeFirst(str) {
            return str.charAt(0).toUpperCase() + str.slice(1);
        }

        // Enhanced Modal Functions
        function showNotImplementedMessage(moduleName) {
            showNotification(`${moduleName} is coming soon! This feature is currently in development.`, 'info');
        }

        function showAddCandidateModal() {
            showNotImplementedMessage('Add Candidate');
        }

        function showAddJobModal() {
            showNotImplementedMessage('Add Job');
        }

        function showScheduleModal() {
            showNotImplementedMessage('Schedule Interview');
        }

        function showQuickAddMenu() {
            showNotImplementedMessage('Quick Add Menu');
        }

        function showImportModal() {
            showNotImplementedMessage('Import Data');
        }

        function showExportModal() {
            showNotImplementedMessage('Export Data');
        }

        function showBulkImport() {
            showNotImplementedMessage('Bulk Import');
        }

        function customizeDashboard() {
            showNotImplementedMessage('Dashboard Customization');
        }

        function showForgotPassword() {
            showNotification('Password reset functionality coming soon!', 'info');
        }

        function showContactAdmin() {
            showNotification('Please contact your system administrator for account access.', 'info');
        }

        function markAllNotificationsRead() {
            showNotification('All notifications marked as read', 'success');
            document.getElementById('notification-badge').textContent = '0';
        }

        function logout() {
            if (confirm('Are you sure you want to sign out?')) {
                showNotification('Signing out...', 'info');
                
                setTimeout(() => {
                    // Reset state
                    currentUser = null;
                    isAuthenticated = false;
                    
                    // Show login page
                    document.getElementById('main-app').classList.add('d-none');
                    document.getElementById('login-page').classList.remove('d-none');
                    
                    // Reset form
                    document.getElementById('login-form').reset();
                    
                    showNotification('Successfully signed out', 'success');
                }, 1000);
            }
        }

        // Enhanced real-time features simulation
        function simulateRealTimeUpdates() {
            setInterval(() => {
                // Simulate notification updates
                const badge = document.getElementById('notification-badge');
                if (badge && Math.random() > 0.8) {
                    const currentCount = parseInt(badge.textContent) || 0;
                    badge.textContent = currentCount + 1;
                }
            }, 30000); // Every 30 seconds
        }

        // Start real-time simulation when app loads
        setTimeout(simulateRealTimeUpdates, 5000);

        // ===== GOOGLE DRIVE INTEGRATION FUNCTIONS =====

        let googleDriveConnected = false;
        let googleDriveConfig = {
            folderId: '',
            syncFrequency: 'manual',
            autoProcess: true,
            lastSync: null,
            totalFiles: 0,
            processedFiles: 0
        };

        function connectToGoogleDrive() {
            const btn = document.getElementById('connectGoogleDriveBtn');
            const statusIndicator = document.getElementById('googleDriveStatus');
            const statusText = document.getElementById('googleDriveStatusText');
            const configDiv = document.getElementById('googleDriveConfig');
            const statsDiv = document.getElementById('googleDriveStats');

            // Simulate connection process
            btn.innerHTML = '<i class="bi bi-hourglass-split me-1"></i>Connecting...';
            btn.disabled = true;

            setTimeout(() => {
                googleDriveConnected = true;

                // Update UI
                statusIndicator.innerHTML = '<i class="bi bi-circle-fill text-success"></i>';
                statusText.textContent = 'Connected';
                btn.innerHTML = '<i class="bi bi-x-circle me-1"></i>Disconnect';
                btn.className = 'btn btn-sm btn-outline-danger';
                btn.onclick = disconnectGoogleDrive;
                btn.disabled = false;

                // Show configuration options
                configDiv.style.display = 'block';
                statsDiv.style.display = 'block';

                // Update stats
                updateGoogleDriveStats();

                showNotification('Successfully connected to Google Drive!', 'success');
            }, 2000);
        }

        function disconnectGoogleDrive() {
            const btn = document.getElementById('connectGoogleDriveBtn');
            const statusIndicator = document.getElementById('googleDriveStatus');
            const statusText = document.getElementById('googleDriveStatusText');
            const configDiv = document.getElementById('googleDriveConfig');
            const statsDiv = document.getElementById('googleDriveStats');

            googleDriveConnected = false;

            // Update UI
            statusIndicator.innerHTML = '<i class="bi bi-circle-fill text-danger"></i>';
            statusText.textContent = 'Disconnected';
            btn.innerHTML = '<i class="bi bi-link me-1"></i>Connect';
            btn.className = 'btn btn-sm btn-primary';
            btn.onclick = connectToGoogleDrive;

            // Hide configuration options
            configDiv.style.display = 'none';
            statsDiv.style.display = 'none';

            showNotification('Disconnected from Google Drive', 'info');
        }

        function testGoogleDriveConnection() {
            if (!googleDriveConnected) {
                showNotification('Please connect to Google Drive first', 'warning');
                return;
            }

            const folderId = document.getElementById('googleDriveFolderId').value;
            if (!folderId) {
                showNotification('Please enter a Google Drive folder ID', 'warning');
                return;
            }

            // Simulate test
            showNotification('Testing connection...', 'info');

            setTimeout(() => {
                googleDriveConfig.folderId = folderId;
                googleDriveConfig.totalFiles = Math.floor(Math.random() * 50) + 10;
                updateGoogleDriveStats();
                showNotification('Connection test successful! Found ' + googleDriveConfig.totalFiles + ' files in the folder.', 'success');
            }, 1500);
        }

        function syncFromGoogleDrive() {
            if (!googleDriveConnected) {
                showNotification('Please connect to Google Drive first', 'warning');
                return;
            }

            if (!googleDriveConfig.folderId) {
                showNotification('Please configure and test the folder connection first', 'warning');
                return;
            }

            // Simulate sync process
            showNotification('Syncing resumes from Google Drive...', 'info');

            setTimeout(() => {
                const newFiles = Math.floor(Math.random() * 10) + 1;
                googleDriveConfig.processedFiles += newFiles;
                googleDriveConfig.lastSync = new Date().toLocaleString();

                updateGoogleDriveStats();

                // Simulate AI processing if enabled
                const autoProcess = document.getElementById('autoProcessResumes').checked;
                if (autoProcess) {
                    setTimeout(() => {
                        showNotification(`Synced ${newFiles} new resumes and processed them with AI analysis!`, 'success');
                    }, 1000);
                } else {
                    showNotification(`Synced ${newFiles} new resumes from Google Drive!`, 'success');
                }
            }, 2000);
        }

        function updateGoogleDriveStats() {
            document.getElementById('totalDriveFiles').textContent = googleDriveConfig.totalFiles;
            document.getElementById('processedDriveFiles').textContent = googleDriveConfig.processedFiles;
            document.getElementById('lastDriveSync').textContent = googleDriveConfig.lastSync || 'Never';
        }

        function refreshGoogleDriveStatus() {
            if (googleDriveConnected) {
                showNotification('Refreshing Google Drive status...', 'info');
                setTimeout(() => {
                    updateGoogleDriveStats();
                    showNotification('Google Drive status refreshed', 'success');
                }, 1000);
            } else {
                showNotification('Google Drive is not connected', 'warning');
            }
        }

        function configureGoogleDrive() {
            if (googleDriveConnected) {
                showNotification('Google Drive configuration is available in the widget below', 'info');
            } else {
                showNotification('Please connect to Google Drive first', 'warning');
            }
        }
    </script>
</body>
</html>

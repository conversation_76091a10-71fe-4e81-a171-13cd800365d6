<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI Resume Analyzer | CubeAI Solutions</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="styles.css">
    <link rel="stylesheet" href="analyzer.css">
    <link rel="stylesheet" href="analyzer-enhanced.css">
</head>
<body>
    <header>
        <nav class="container">
            <a href="#" class="logo">
                <i class="fas fa-cube"></i> CubeAI Resume Analyzer
            </a>
            <ul class="nav-links">
                <li><a href="#home">Home</a></li>
                <li><a href="#analyzer">Analyzer</a></li>
                <li><a href="#about">About</a></li>
                <li><a href="#contact">Contact</a></li>
            </ul>
        </nav>
    </header>

    <main>
        <section class="hero">
            <div class="container">
                <h1>AI-Powered Resume Analyzer</h1>
                <p>Get instant insights, improve your resume, and land your dream job with our advanced AI technology</p>
            </div>
        </section>

        <div class="container">
            <div class="app-section">
                <div class="app-header">
                    <h2><i class="fas fa-robot"></i> Intelligent Resume Analysis</h2>
                    <p>Upload your resume and let our AI provide comprehensive feedback and improvement suggestions</p>
                </div>

                <div class="app-content">
                    <!-- Job Description Section -->
                    <div class="job-description-section mb-4">
                        <div class="card">
                            <div class="card-header bg-primary text-white">
                                <h5 class="mb-0"><i class="fas fa-briefcase me-2"></i>Job Description Analysis</h5>
                            </div>
                            <div class="card-body">
                                <label for="jobDescription" class="form-label fw-bold">Job Description (Paste or type the requirements):</label>
                                <textarea id="jobDescription" class="form-control" rows="8" placeholder="Enter the job description or requirements here for intelligent matching analysis..."></textarea>
                                <small class="form-text text-muted">Our AI will analyze this job description to provide personalized matching scores and recommendations.</small>
                            </div>
                        </div>
                    </div>

                    <!-- Upload Section -->
                    <div class="upload-section">
                        <div class="card">
                            <div class="card-header bg-success text-white">
                                <h5 class="mb-0"><i class="fas fa-upload me-2"></i>Resume Upload & Analysis</h5>
                            </div>
                            <div class="card-body">
                                <div class="upload-area" id="uploadArea" tabindex="0" role="button" aria-label="Upload your CVs here. Press Enter to open file dialog.">
                                    <div class="upload-icon">
                                        <i class="fas fa-cloud-upload-alt"></i>
                                    </div>
                                    <h3>Drop your CVs here or click to browse</h3>
                                    <p>Supports PDF, DOC, DOCX files (Max 5MB each, multiple files allowed)</p>
                                    <input type="file" id="fileInput" class="file-input" accept=".pdf,.doc,.docx" multiple>
                                    <button class="btn btn-primary btn-lg mt-3" onclick="document.getElementById('fileInput').click()">
                                        <i class="fas fa-upload me-2"></i> Start AI Analysis
                                    </button>
                                </div>

                                <!-- File List Display -->
                                <div id="fileList" class="mt-3"></div>

                                <!-- Error Messages -->
                                <div id="errorMessage" class="mt-3" style="display: none;"></div>

                                <!-- Action Buttons -->
                                <div class="mt-3 d-flex gap-2 flex-wrap">
                                    <button class="btn btn-outline-primary" onclick="window.cubeAIAnalyzer?.exportResults('json')">
                                        <i class="fas fa-download me-2"></i>Export JSON
                                    </button>
                                    <button class="btn btn-outline-success" onclick="window.cubeAIAnalyzer?.exportResults('csv')">
                                        <i class="fas fa-file-csv me-2"></i>Export CSV
                                    </button>
                                    <button class="btn btn-outline-danger" onclick="window.cubeAIAnalyzer?.clearResults()">
                                        <i class="fas fa-trash me-2"></i>Clear All
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- AI Analysis Results Container -->
                    <div id="analysisResults" class="mt-4" style="display: none;">
                        <!-- Results will be dynamically generated by the AI analyzer -->
                    </div>

                    <!-- Loading Indicator -->
                    <div id="loadingIndicator" class="text-center p-4" style="display: none;">
                        <div class="spinner-border text-primary" role="status">
                            <span class="visually-hidden">Loading...</span>
                        </div>
                        <p class="mt-2">🤖 AI is analyzing your resume...</p>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
    <script src="demo-data.js"></script>
    <script src="analyzer.js"></script>
</body>
</html>
/**
 * CubeAI HR AI Automation Agent - Intelligent Recruitment System
 * Features: Database Connection, CV Management, Job Creation, AI Analysis, AI Reports
 */

class CubeAIHRAutomationAgent {
    constructor() {
        this.databaseService = new DatabaseService();
        this.externalDbConfig = null;
        this.isExternalDbConnected = false;
        this.candidates = [];
        this.jobs = [];
        this.analysisResults = [];
        this.selectedCandidates = [];
        this.currentJob = null;

        this.init();
    }

    async init() {
        console.log('🚀 Initializing CubeAI HR AI Automation Agent...');

        try {
            // Connect to internal demo database
            await this.databaseService.connect();

            // Load initial data
            await this.loadCandidates();
            await this.loadJobs();
            await this.loadAnalysisResults();

            // Setup event listeners
            this.setupEventListeners();

            // Update UI
            this.updateDashboardStats();
            this.renderCVDatabase();
            this.renderExistingJobs();
            this.populateJobSelects();
            this.updateConnectionStatus();

            console.log('✅ HR AI Automation Agent initialized successfully');

        } catch (error) {
            console.error('❌ HR AI Automation Agent initialization failed:', error);
            this.showError('Failed to initialize AI agent: ' + error.message);
        }
    }

    // ===== DATABASE CONNECTION METHODS =====

    async testDatabaseConnection() {
        try {
            const config = this.getDbConfigFromForm();

            this.showNotification('Testing database connection...', 'info');

            // Simulate connection test
            await new Promise(resolve => setTimeout(resolve, 2000));

            if (config.dbType === 'demo') {
                this.showSuccess('Demo database connection successful!');
                return true;
            }

            // For real databases, implement actual connection testing
            this.showSuccess('Database connection test successful!');
            return true;

        } catch (error) {
            console.error('❌ Database connection test failed:', error);
            this.showError('Connection test failed: ' + error.message);
            return false;
        }
    }

    async connectToDatabase() {
        try {
            const config = this.getDbConfigFromForm();

            this.showNotification('Connecting to external database...', 'info');

            // Store external database configuration
            this.externalDbConfig = config;

            // Simulate connection process
            await new Promise(resolve => setTimeout(resolve, 3000));

            this.isExternalDbConnected = true;
            this.updateConnectionStatus();

            // Auto-sync if enabled
            if (document.getElementById('autoSync')?.checked) {
                await this.syncFromExternalDB();
            }

            this.showSuccess('Successfully connected to external database!');

        } catch (error) {
            console.error('❌ Database connection failed:', error);
            this.showError('Failed to connect to database: ' + error.message);
        }
    }

    getDbConfigFromForm() {
        return {
            dbType: document.getElementById('dbType').value,
            host: document.getElementById('dbHost').value,
            port: document.getElementById('dbPort').value,
            database: document.getElementById('dbName').value,
            username: document.getElementById('dbUsername').value,
            password: document.getElementById('dbPassword').value
        };
    }

    updateConnectionStatus() {
        const statusElement = document.getElementById('connectionStatus');
        const externalDbStatusElement = document.getElementById('externalDbStatus');

        if (this.isExternalDbConnected) {
            if (statusElement) {
                statusElement.innerHTML = `
                    <i class="bi bi-check-circle me-2"></i>
                    Connected to ${this.externalDbConfig?.dbType || 'external'} database
                `;
                statusElement.className = 'alert alert-success';
            }

            if (externalDbStatusElement) {
                externalDbStatusElement.innerHTML = `
                    <i class="bi bi-check-circle me-2"></i>
                    Connected to ${this.externalDbConfig?.dbType || 'external'} database
                `;
                externalDbStatusElement.className = 'alert alert-success';
            }
        } else {
            if (statusElement) {
                statusElement.innerHTML = `
                    <i class="bi bi-info-circle me-2"></i>
                    Using internal demo database
                `;
                statusElement.className = 'alert alert-info';
            }

            if (externalDbStatusElement) {
                externalDbStatusElement.innerHTML = `
                    <i class="bi bi-exclamation-triangle me-2"></i>
                    Connect to external database first
                `;
                externalDbStatusElement.className = 'alert alert-warning';
            }
        }
    }

    // ===== EXTERNAL DATABASE SYNC METHODS =====

    async syncFromExternalDB() {
        try {
            if (!this.isExternalDbConnected) {
                this.showError('Please connect to external database first');
                return;
            }

            this.showNotification('Syncing data from external database...', 'info');

            // Simulate data sync
            await new Promise(resolve => setTimeout(resolve, 2000));

            // Simulate loading new candidates and jobs
            const newCandidates = this.generateSampleCandidates(3);
            const newJobs = this.generateSampleJobs(2);

            this.candidates.push(...newCandidates);
            this.jobs.push(...newJobs);

            // Update UI
            this.renderCVDatabase();
            this.renderExistingJobs();
            this.updateDashboardStats();

            // Auto AI analysis if enabled
            if (document.getElementById('autoAIAnalysis')?.checked) {
                await this.runFullAIAnalysis();
            }

            this.showSuccess(`Synced ${newCandidates.length} candidates and ${newJobs.length} jobs from external database`);

        } catch (error) {
            console.error('❌ External database sync failed:', error);
            this.showError('Failed to sync from external database: ' + error.message);
        }
    }

    async pushToExternalDB() {
        try {
            if (!this.isExternalDbConnected) {
                this.showError('Please connect to external database first');
                return;
            }

            this.showNotification('Pushing data to external database...', 'info');

            // Simulate data push
            await new Promise(resolve => setTimeout(resolve, 2000));

            this.showSuccess(`Pushed ${this.candidates.length} candidates and ${this.jobs.length} jobs to external database`);

        } catch (error) {
            console.error('❌ External database push failed:', error);
            this.showError('Failed to push to external database: ' + error.message);
        }
    }

    async runFullAIAnalysis() {
        try {
            this.showNotification('Running full AI analysis on all data...', 'info');

            // Clear previous results
            this.analysisResults = [];

            // Analyze all candidates against all jobs
            for (const job of this.jobs) {
                for (const candidate of this.candidates) {
                    const analysis = await this.performAIAnalysis(candidate, job);
                    this.analysisResults.push(analysis);
                }
            }

            this.showSuccess(`AI analysis completed for ${this.analysisResults.length} candidate-job combinations`);

        } catch (error) {
            console.error('❌ Full AI analysis failed:', error);
            this.showError('Failed to run full AI analysis: ' + error.message);
        }
    }

    async syncCandidates() {
        await this.syncFromExternalDB();
    }

    async syncJobs() {
        await this.syncFromExternalDB();
    }

    async runAIAnalysis() {
        await this.runFullAIAnalysis();
    }

    async generateAIReport() {
        try {
            this.showNotification('Generating AI report...', 'info');

            // Simulate report generation
            await new Promise(resolve => setTimeout(resolve, 1500));

            this.showSuccess('AI report generated successfully!');

            // Switch to AI Reports tab
            const reportsTab = document.getElementById('ai-reports-tab');
            if (reportsTab) {
                reportsTab.click();
            }

        } catch (error) {
            console.error('❌ AI report generation failed:', error);
            this.showError('Failed to generate AI report: ' + error.message);
        }
    }

    // ===== SAMPLE DATA GENERATION METHODS =====

    generateSampleCandidates(count) {
        const tamilNames = ['Arjun Kumar', 'Priya Sharma', 'Karthik Raj', 'Divya Nair', 'Ravi Krishnan'];
        const positions = ['Software Engineer', 'Data Analyst', 'Product Manager', 'UI/UX Designer', 'DevOps Engineer'];
        const skills = [
            ['JavaScript', 'React', 'Node.js'],
            ['Python', 'Data Science', 'Machine Learning'],
            ['Product Management', 'Agile', 'Scrum'],
            ['UI Design', 'Figma', 'Adobe XD'],
            ['Docker', 'Kubernetes', 'AWS']
        ];

        const candidates = [];
        for (let i = 0; i < count; i++) {
            const randomIndex = Math.floor(Math.random() * tamilNames.length);
            candidates.push({
                id: `ext_${Date.now()}_${i}`,
                name: tamilNames[randomIndex],
                email: `${tamilNames[randomIndex].toLowerCase().replace(' ', '.')}@external.com`,
                phone: `+91 ${Math.floor(Math.random() * 9000000000) + 1000000000}`,
                position: positions[randomIndex],
                experience: Math.floor(Math.random() * 10) + 1,
                skills: skills[randomIndex],
                resumeText: `Experienced ${positions[randomIndex]} with ${Math.floor(Math.random() * 10) + 1} years of experience in ${skills[randomIndex].join(', ')}.`,
                source: 'External Database'
            });
        }
        return candidates;
    }

    generateSampleJobs(count) {
        const jobTitles = ['Senior Software Engineer', 'Data Scientist', 'Product Manager', 'UX Designer', 'Cloud Engineer'];
        const departments = ['Engineering', 'Data Science', 'Product', 'Design', 'Infrastructure'];

        const jobs = [];
        for (let i = 0; i < count; i++) {
            const randomIndex = Math.floor(Math.random() * jobTitles.length);
            jobs.push({
                id: `ext_job_${Date.now()}_${i}`,
                title: jobTitles[randomIndex],
                department: departments[randomIndex],
                location: 'Chennai, Tamil Nadu',
                type: 'Full-time',
                experience: `${Math.floor(Math.random() * 5) + 2}-${Math.floor(Math.random() * 5) + 7} years`,
                requiredSkills: ['JavaScript', 'React', 'Node.js', 'Python'],
                atsKeywords: ['experience', 'skills', 'team', 'project'],
                description: `We are looking for a ${jobTitles[randomIndex]} to join our ${departments[randomIndex]} team.`,
                source: 'External Database'
            });
        }
        return jobs;
    }

    // ===== DATA LOADING METHODS =====

    async loadCandidates() {
        try {
            this.candidates = await this.databaseService.getAllCandidates();
            console.log(`📋 Loaded ${this.candidates.length} candidates`);
        } catch (error) {
            console.error('❌ Failed to load candidates:', error);
            this.candidates = [];
        }
    }

    async loadJobs() {
        try {
            this.jobs = await this.databaseService.getAllJobs();
            console.log(`💼 Loaded ${this.jobs.length} jobs`);
        } catch (error) {
            console.error('❌ Failed to load jobs:', error);
            this.jobs = [];
        }
    }

    async loadAnalysisResults() {
        try {
            this.analysisResults = await this.databaseService.getAnalysisResults();
            console.log(`📊 Loaded ${this.analysisResults.length} analysis results`);
        } catch (error) {
            console.error('❌ Failed to load analysis results:', error);
            this.analysisResults = [];
        }
    }

    // ===== EVENT LISTENERS SETUP =====

    setupEventListeners() {
        // Bulk file upload
        this.setupBulkUpload();
        
        // Manual CV form (removed - now using AI Database Sync)
        // document.getElementById('manualCVForm') - element no longer exists

        // Job creation form
        const jobCreationForm = document.getElementById('jobCreationForm');
        if (jobCreationForm) {
            jobCreationForm.addEventListener('submit', (e) => {
                e.preventDefault();
                this.handleJobCreation();
            });
        }

        // CV database controls
        const refreshCVs = document.getElementById('refreshCVs');
        if (refreshCVs) {
            refreshCVs.addEventListener('click', () => {
                this.refreshCVDatabase();
            });
        }

        const exportCVs = document.getElementById('exportCVs');
        if (exportCVs) {
            exportCVs.addEventListener('click', () => {
                this.exportCVData();
            });
        }

        // Search and filters
        const cvSearchInput = document.getElementById('cvSearchInput');
        if (cvSearchInput) {
            cvSearchInput.addEventListener('input', (e) => {
                this.filterCVDatabase();
            });
        }

        const experienceFilter = document.getElementById('experienceFilter');
        if (experienceFilter) {
            experienceFilter.addEventListener('change', () => {
                this.filterCVDatabase();
            });
        }

        const skillsFilter = document.getElementById('skillsFilter');
        if (skillsFilter) {
            skillsFilter.addEventListener('change', () => {
                this.filterCVDatabase();
            });
        }

        // AI Analysis
        const analyzeAllBtn = document.getElementById('analyzeAllBtn');
        if (analyzeAllBtn) {
            analyzeAllBtn.addEventListener('click', () => {
                this.performBulkAIAnalysis();
            });
        }

        const analyzeSelectedBtn = document.getElementById('analyzeSelectedBtn');
        if (analyzeSelectedBtn) {
            analyzeSelectedBtn.addEventListener('click', () => {
                this.performSelectedAIAnalysis();
            });
        }

        // HR Reports
        const generateDetailedReport = document.getElementById('generateDetailedReport');
        if (generateDetailedReport) {
            generateDetailedReport.addEventListener('click', () => {
                this.generateDetailedHRReport();
            });
        }

        const exportCandidateData = document.getElementById('exportCandidateData');
        if (exportCandidateData) {
            exportCandidateData.addEventListener('click', () => {
                this.exportCandidateData();
            });
        }

        // AI Analysis page specific listeners
        const analysisJobSelect = document.getElementById('analysisJobSelect');
        if (analysisJobSelect) {
            analysisJobSelect.addEventListener('change', () => {
                this.displayJobDescription();
            });
        }

        // AI Chatbot listeners
        const sendChatBtn = document.getElementById('sendChatBtn');
        if (sendChatBtn) {
            sendChatBtn.addEventListener('click', () => {
                this.sendChatMessage();
            });
        }

        const chatInput = document.getElementById('chatInput');
        if (chatInput) {
            chatInput.addEventListener('keypress', (e) => {
                if (e.key === 'Enter') {
                    this.sendChatMessage();
                }
            });
        }
    }

    setupBulkUpload() {
        const uploadArea = document.getElementById('bulkUploadArea');
        const fileInput = document.getElementById('bulkFileInput');

        // Drag and drop events
        uploadArea.addEventListener('dragover', (e) => {
            e.preventDefault();
            uploadArea.style.background = '#e9ecef';
        });

        uploadArea.addEventListener('dragleave', () => {
            uploadArea.style.background = '#f8f9fa';
        });

        uploadArea.addEventListener('drop', (e) => {
            e.preventDefault();
            uploadArea.style.background = '#f8f9fa';
            const files = Array.from(e.dataTransfer.files);
            this.handleBulkFileUpload(files);
        });

        // File input change
        fileInput.addEventListener('change', (e) => {
            const files = Array.from(e.target.files);
            this.handleBulkFileUpload(files);
        });

        // Click to upload
        uploadArea.addEventListener('click', () => {
            fileInput.click();
        });
    }

    // ===== CV MANAGEMENT METHODS =====

    async handleBulkFileUpload(files) {
        if (files.length === 0) return;

        const progressContainer = document.getElementById('uploadProgress');
        const progressBar = document.getElementById('uploadProgressBar');
        const statusDiv = document.getElementById('uploadStatus');

        progressContainer.style.display = 'block';
        
        try {
            let processedCount = 0;
            const totalFiles = files.length;

            for (const file of files) {
                try {
                    // Update progress
                    const progress = (processedCount / totalFiles) * 100;
                    progressBar.style.width = `${progress}%`;
                    statusDiv.textContent = `Processing ${processedCount + 1} of ${totalFiles}: ${file.name}`;

                    // Read file content
                    const content = await this.readFileContent(file);
                    
                    // Extract candidate info from filename and content
                    const candidateData = await this.extractCandidateFromFile(file, content);
                    
                    // Add to database
                    await this.databaseService.addCandidate(candidateData);
                    
                    processedCount++;
                    
                } catch (error) {
                    console.error(`❌ Failed to process file ${file.name}:`, error);
                }
            }

            // Complete
            progressBar.style.width = '100%';
            statusDiv.textContent = `✅ Successfully processed ${processedCount} of ${totalFiles} files`;

            // Refresh data
            await this.loadCandidates();
            this.renderCVDatabase();
            this.updateDashboardStats();

            setTimeout(() => {
                progressContainer.style.display = 'none';
            }, 3000);

            this.showSuccess(`Successfully uploaded ${processedCount} CVs to database`);

        } catch (error) {
            console.error('❌ Bulk upload failed:', error);
            this.showError('Bulk upload failed: ' + error.message);
            progressContainer.style.display = 'none';
        }
    }

    async readFileContent(file) {
        return new Promise((resolve, reject) => {
            const reader = new FileReader();
            reader.onload = (e) => resolve(e.target.result);
            reader.onerror = (e) => reject(e);
            reader.readAsText(file);
        });
    }

    async extractCandidateFromFile(file, content) {
        // Extract candidate information from file content
        const lines = content.split('\n');
        const candidateName = this.extractNameFromContent(lines) || this.extractNameFromFilename(file.name);
        const email = this.extractEmailFromContent(lines);
        const phone = this.extractPhoneFromContent(lines);
        const skills = this.extractSkillsFromContent(content);
        const experience = this.extractExperienceFromContent(content);

        return {
            name: candidateName,
            email: email || `${candidateName.toLowerCase().replace(/\s+/g, '.')}@email.com`,
            phone: phone || '+91-98765-00000',
            position: this.extractPositionFromContent(lines) || 'Software Engineer',
            location: 'Chennai, Tamil Nadu',
            experienceYears: experience,
            status: 'active',
            skills: skills,
            education: this.extractEducationFromContent(lines) || 'B.E. Computer Science',
            resumeText: content,
            score: 0,
            source: 'bulk_upload'
        };
    }

    extractNameFromContent(lines) {
        // Look for name in first few lines
        for (let i = 0; i < Math.min(5, lines.length); i++) {
            const line = lines[i].trim();
            if (line && !line.includes('@') && !line.includes('+') && line.length > 3 && line.length < 50) {
                // Check if it looks like a name (contains letters and possibly spaces)
                if (/^[a-zA-Z\s]+$/.test(line)) {
                    return line;
                }
            }
        }
        return null;
    }

    extractNameFromFilename(filename) {
        // Extract name from filename, removing extension and common prefixes
        return filename
            .replace(/\.(pdf|doc|docx|txt)$/i, '')
            .replace(/^(resume|cv)[-_\s]*/i, '')
            .replace(/[-_]/g, ' ')
            .trim();
    }

    extractEmailFromContent(lines) {
        const emailRegex = /\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b/;
        for (const line of lines) {
            const match = line.match(emailRegex);
            if (match) return match[0];
        }
        return null;
    }

    extractPhoneFromContent(lines) {
        const phoneRegex = /(\+91[-\s]?)?[6-9]\d{9}|\+91[-\s]?\d{5}[-\s]?\d{5}/;
        for (const line of lines) {
            const match = line.match(phoneRegex);
            if (match) return match[0];
        }
        return null;
    }

    extractSkillsFromContent(content) {
        const commonSkills = [
            'JavaScript', 'Python', 'Java', 'React', 'Node.js', 'Angular', 'Vue.js',
            'HTML', 'CSS', 'MongoDB', 'MySQL', 'PostgreSQL', 'AWS', 'Docker',
            'Kubernetes', 'Git', 'Jenkins', 'Machine Learning', 'TensorFlow',
            'PyTorch', 'Data Science', 'AI', 'DevOps', 'Agile', 'Scrum'
        ];

        const foundSkills = [];
        const contentLower = content.toLowerCase();

        for (const skill of commonSkills) {
            if (contentLower.includes(skill.toLowerCase())) {
                foundSkills.push(skill);
            }
        }

        return foundSkills;
    }

    extractExperienceFromContent(content) {
        const expRegex = /(\d+)[\s]*(?:years?|yrs?)\s*(?:of\s*)?(?:experience|exp)/i;
        const match = content.match(expRegex);
        return match ? parseInt(match[1]) : 2; // Default to 2 years
    }

    extractPositionFromContent(lines) {
        const commonPositions = [
            'Software Engineer', 'Data Scientist', 'Frontend Developer', 'Backend Developer',
            'Full Stack Developer', 'DevOps Engineer', 'UI/UX Designer', 'Product Manager',
            'Business Analyst', 'QA Engineer', 'Mobile Developer'
        ];

        for (const line of lines.slice(0, 10)) {
            for (const position of commonPositions) {
                if (line.toLowerCase().includes(position.toLowerCase())) {
                    return position;
                }
            }
        }
        return null;
    }

    extractEducationFromContent(lines) {
        const educationKeywords = ['university', 'college', 'institute', 'degree', 'bachelor', 'master', 'phd'];
        
        for (const line of lines) {
            const lineLower = line.toLowerCase();
            if (educationKeywords.some(keyword => lineLower.includes(keyword))) {
                return line.trim();
            }
        }
        return null;
    }

    // handleManualCVSubmit method removed - replaced with AI Database Sync functionality

    // ===== AI ANALYSIS PAGE METHODS =====

    displayJobDescription() {
        const jobSelect = document.getElementById('analysisJobSelect');
        const jobDescriptionDisplay = document.getElementById('jobDescriptionDisplay');

        if (!jobSelect || !jobDescriptionDisplay) return;

        const selectedJobId = jobSelect.value;
        if (!selectedJobId) {
            jobDescriptionDisplay.innerHTML = `
                <div class="text-muted text-center">
                    <i class="bi bi-briefcase display-4"></i>
                    <p class="mt-2">Select a job role to view description</p>
                </div>
            `;
            return;
        }

        const job = this.jobs.find(j => j.id === selectedJobId);
        if (!job) return;

        jobDescriptionDisplay.innerHTML = `
            <div class="job-description-content">
                <div class="d-flex align-items-center mb-3">
                    <i class="bi bi-briefcase text-primary me-2"></i>
                    <h5 class="mb-0">${job.title}</h5>
                </div>

                <div class="row mb-3">
                    <div class="col-md-6">
                        <small class="text-muted">Department:</small>
                        <p class="mb-1">${job.department}</p>
                    </div>
                    <div class="col-md-6">
                        <small class="text-muted">Experience Required:</small>
                        <p class="mb-1">${job.experienceRequired}</p>
                    </div>
                </div>

                <div class="mb-3">
                    <small class="text-muted">Description:</small>
                    <p class="mb-2">${job.description}</p>
                </div>

                <div class="mb-3">
                    <small class="text-muted">AI Keywords:</small>
                    <div class="d-flex flex-wrap gap-1 mt-1">
                        ${job.keywords.map(keyword =>
                            `<span class="badge bg-primary">${keyword}</span>`
                        ).join('')}
                    </div>
                </div>

                <div class="mb-3">
                    <small class="text-muted">Required Skills:</small>
                    <div class="d-flex flex-wrap gap-1 mt-1">
                        ${job.requiredSkills.map(skill =>
                            `<span class="badge bg-success">${skill}</span>`
                        ).join('')}
                    </div>
                </div>

                <div class="alert alert-info">
                    <i class="bi bi-info-circle me-2"></i>
                    <strong>AI Analysis:</strong> This job description will be used to score and rank candidates based on skill matching, experience relevance, and keyword alignment.
                </div>
            </div>
        `;
    }

    sendChatMessage() {
        const chatInput = document.getElementById('chatInput');
        const chatMessages = document.getElementById('chatMessages');

        if (!chatInput || !chatMessages) return;

        const message = chatInput.value.trim();
        if (!message) return;

        // Add user message
        this.addChatMessage('user', message);

        // Clear input
        chatInput.value = '';

        // Simulate AI response
        setTimeout(() => {
            const aiResponse = this.generateAIResponse(message);
            this.addChatMessage('bot', aiResponse);
        }, 1000);
    }

    addChatMessage(sender, message) {
        const chatMessages = document.getElementById('chatMessages');
        if (!chatMessages) return;

        const messageDiv = document.createElement('div');
        messageDiv.className = `chat-message ${sender}-message mb-2`;

        if (sender === 'user') {
            messageDiv.innerHTML = `
                <div class="d-flex align-items-start justify-content-end">
                    <div class="bg-primary text-white rounded p-2 shadow-sm me-2" style="max-width: 80%;">
                        <small class="text-light">You</small>
                        <p class="mb-0">${message}</p>
                    </div>
                    <div class="bg-primary text-white rounded-circle p-2" style="width: 32px; height: 32px; display: flex; align-items: center; justify-content: center;">
                        <i class="bi bi-person" style="font-size: 14px;"></i>
                    </div>
                </div>
            `;
        } else {
            messageDiv.innerHTML = `
                <div class="d-flex align-items-start">
                    <div class="bg-success text-white rounded-circle p-2 me-2" style="width: 32px; height: 32px; display: flex; align-items: center; justify-content: center;">
                        <i class="bi bi-robot" style="font-size: 14px;"></i>
                    </div>
                    <div class="bg-white rounded p-2 shadow-sm" style="max-width: 80%;">
                        <small class="text-muted">AI Assistant</small>
                        <p class="mb-0">${message}</p>
                    </div>
                </div>
            `;
        }

        chatMessages.appendChild(messageDiv);
        chatMessages.scrollTop = chatMessages.scrollHeight;
    }

    generateAIResponse(userMessage) {
        const message = userMessage.toLowerCase();

        // Simple AI response logic based on keywords
        if (message.includes('analyze') || message.includes('analysis')) {
            return `I can help you analyze resumes! Currently, I have ${this.candidates.length} candidates in the database. Would you like me to run an AI analysis for a specific job role or analyze all candidates?`;
        }

        if (message.includes('candidate') || message.includes('resume')) {
            const topCandidates = this.candidates
                .sort((a, b) => (b.score || 0) - (a.score || 0))
                .slice(0, 3);

            return `Here are the top 3 candidates based on AI scoring: ${topCandidates.map(c => `${c.name} (${c.score || 0}% match)`).join(', ')}. Would you like detailed insights on any of them?`;
        }

        if (message.includes('job') || message.includes('position')) {
            return `I can see ${this.jobs.length} active job positions. The most in-demand roles are: ${this.jobs.slice(0, 3).map(j => j.title).join(', ')}. Which position would you like to focus on for candidate matching?`;
        }

        if (message.includes('score') || message.includes('rating')) {
            const avgScore = this.candidates.reduce((sum, c) => sum + (c.score || 0), 0) / this.candidates.length;
            return `The average AI score across all candidates is ${avgScore.toFixed(1)}%. Candidates are scored based on skills (40%), experience (25%), keywords (20%), education (10%), and cultural fit (5%).`;
        }

        if (message.includes('skill') || message.includes('technology')) {
            const skillCounts = {};
            this.candidates.forEach(c => {
                c.skills?.forEach(skill => {
                    skillCounts[skill] = (skillCounts[skill] || 0) + 1;
                });
            });
            const topSkills = Object.entries(skillCounts)
                .sort(([,a], [,b]) => b - a)
                .slice(0, 5)
                .map(([skill]) => skill);

            return `The most common skills in our candidate pool are: ${topSkills.join(', ')}. This data helps identify talent trends and skill gaps in the market.`;
        }

        if (message.includes('help') || message.includes('what can you do')) {
            return `I can help you with: 📊 Resume analysis and scoring, 🎯 Candidate-job matching, 📈 Recruitment insights and trends, 🔍 Skill gap analysis, 📋 Interview recommendations. What would you like to explore?`;
        }

        // Default response
        return `I understand you're asking about "${userMessage}". I'm here to help with resume analysis, candidate insights, and recruitment optimization. Could you be more specific about what you'd like to know?`;
    }

    // ===== UI RENDERING METHODS =====

    renderCVDatabase() {
        const container = document.getElementById('cvDatabaseList');
        
        if (this.candidates.length === 0) {
            container.innerHTML = `
                <div class="text-center py-4">
                    <i class="bi bi-inbox display-4 text-muted"></i>
                    <h6 class="mt-3 text-muted">No candidates in database</h6>
                    <p class="text-muted">Upload CVs or add candidates manually to get started</p>
                </div>
            `;
            return;
        }

        container.innerHTML = this.candidates.map(candidate => `
            <div class="candidate-card card mb-2">
                <div class="card-body">
                    <div class="row align-items-center">
                        <div class="col-md-1">
                            <input type="checkbox" class="form-check-input candidate-checkbox" value="${candidate.id}">
                        </div>
                        <div class="col-md-3">
                            <h6 class="mb-1">${candidate.name}</h6>
                            <small class="text-muted">${candidate.email}</small>
                        </div>
                        <div class="col-md-2">
                            <span class="badge bg-primary">${candidate.position}</span>
                        </div>
                        <div class="col-md-2">
                            <small>${candidate.experienceYears} years exp</small>
                        </div>
                        <div class="col-md-3">
                            <div class="skills-container">
                                ${candidate.skills.slice(0, 3).map(skill => 
                                    `<span class="skill-match skill-matched">${skill}</span>`
                                ).join('')}
                                ${candidate.skills.length > 3 ? `<span class="text-muted">+${candidate.skills.length - 3} more</span>` : ''}
                            </div>
                        </div>
                        <div class="col-md-1">
                            <button class="btn btn-sm btn-outline-primary" onclick="hrDashboard.viewCandidate('${candidate.id}')">
                                <i class="bi bi-eye"></i>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        `).join('');

        // Update checkbox listeners
        document.querySelectorAll('.candidate-checkbox').forEach(checkbox => {
            checkbox.addEventListener('change', () => {
                this.updateSelectedCandidates();
            });
        });

        // Update total count
        document.getElementById('totalCVs').textContent = this.candidates.length;
    }

    updateSelectedCandidates() {
        const checkboxes = document.querySelectorAll('.candidate-checkbox:checked');
        this.selectedCandidates = Array.from(checkboxes).map(cb => cb.value);
        
        const analyzeSelectedBtn = document.getElementById('analyzeSelectedBtn');
        analyzeSelectedBtn.disabled = this.selectedCandidates.length === 0;
        analyzeSelectedBtn.innerHTML = `
            <i class="bi bi-check-square me-2"></i>
            Analyze Selected (${this.selectedCandidates.length})
        `;
    }

    updateDashboardStats() {
        document.getElementById('totalCandidatesCount').textContent = this.candidates.length;
        document.getElementById('activeJobsCount').textContent = this.jobs.length;
        
        const qualifiedCount = this.analysisResults.filter(r => (r.overallScore || 0) > 80).length;
        document.getElementById('qualifiedCandidatesCount').textContent = qualifiedCount;
        
        const avgScore = this.analysisResults.length > 0 
            ? (this.analysisResults.reduce((sum, r) => sum + (r.overallScore || 0), 0) / this.analysisResults.length).toFixed(1)
            : 0;
        document.getElementById('averageATSScore').textContent = avgScore;
    }

    // ===== JOB MANAGEMENT METHODS =====

    async handleJobCreation() {
        try {
            const jobData = {
                title: document.getElementById('jobTitle').value,
                department: document.getElementById('jobDepartment').value,
                experienceRequired: document.getElementById('jobExperience').value,
                jobType: document.getElementById('jobType').value,
                location: document.getElementById('jobLocation').value,
                requiredSkills: document.getElementById('jobSkills').value.split(',').map(s => s.trim()).filter(s => s),
                description: document.getElementById('jobDescription').value,
                atsKeywords: document.getElementById('jobATSKeywords').value.split(',').map(s => s.trim()).filter(s => s),
                status: 'active',
                createdDate: new Date().toISOString(),
                postedBy: 'HR Team'
            };

            await this.databaseService.addJob(jobData);

            // Reset form
            document.getElementById('jobCreationForm').reset();

            // Refresh data
            await this.loadJobs();
            this.renderExistingJobs();
            this.populateJobSelects();
            this.updateDashboardStats();

            this.showSuccess('Job role created successfully');

        } catch (error) {
            console.error('❌ Failed to create job:', error);
            this.showError('Failed to create job: ' + error.message);
        }
    }

    renderExistingJobs() {
        const container = document.getElementById('existingJobsList');

        if (this.jobs.length === 0) {
            container.innerHTML = `
                <div class="text-center py-3">
                    <i class="bi bi-briefcase text-muted"></i>
                    <p class="text-muted mt-2">No jobs created yet</p>
                </div>
            `;
            return;
        }

        container.innerHTML = this.jobs.map(job => `
            <div class="card mb-2">
                <div class="card-body p-3">
                    <h6 class="card-title mb-1">${job.title}</h6>
                    <small class="text-muted">${job.department} • ${job.experienceRequired}</small>
                    <div class="mt-2">
                        <span class="badge bg-light text-dark">${job.jobType}</span>
                        <span class="badge bg-secondary">${job.location}</span>
                    </div>
                    <div class="mt-2">
                        <button class="btn btn-sm btn-outline-primary" onclick="hrDashboard.editJob('${job.id}')">
                            <i class="bi bi-pencil"></i>
                        </button>
                        <button class="btn btn-sm btn-outline-danger" onclick="hrDashboard.deleteJob('${job.id}')">
                            <i class="bi bi-trash"></i>
                        </button>
                    </div>
                </div>
            </div>
        `).join('');
    }

    populateJobSelects() {
        const analysisJobSelect = document.getElementById('analysisJobSelect');

        if (analysisJobSelect) {
            analysisJobSelect.innerHTML = '<option value="">Choose a job role for analysis...</option>' +
                this.jobs.map(job => `
                    <option value="${job.id}">${job.title} - ${job.department}</option>
                `).join('');
        }
    }

    // ===== AI ANALYSIS METHODS =====

    async performBulkAIAnalysis() {
        const jobId = document.getElementById('analysisJobSelect').value;
        if (!jobId) {
            this.showError('Please select a job role first');
            return;
        }

        const job = this.jobs.find(j => j.id === jobId);
        if (!job) {
            this.showError('Selected job not found');
            return;
        }

        this.currentJob = job;
        await this.analyzeAllCandidates();
    }

    async performSelectedAIAnalysis() {
        if (this.selectedCandidates.length === 0) {
            this.showError('Please select candidates first');
            return;
        }

        const jobId = document.getElementById('analysisJobSelect').value;
        if (!jobId) {
            this.showError('Please select a job role first');
            return;
        }

        const job = this.jobs.find(j => j.id === jobId);
        if (!job) {
            this.showError('Selected job not found');
            return;
        }

        this.currentJob = job;
        await this.analyzeSelectedCandidates();
    }

    async analyzeAllCandidates() {
        const progressContainer = document.getElementById('analysisProgress');
        const progressBar = document.getElementById('analysisProgressBar');
        const statusDiv = document.getElementById('analysisStatus');

        progressContainer.style.display = 'block';

        try {
            const totalCandidates = this.candidates.length;
            let processedCount = 0;

            for (const candidate of this.candidates) {
                try {
                    // Update progress
                    const progress = (processedCount / totalCandidates) * 100;
                    progressBar.style.width = `${progress}%`;
                    statusDiv.textContent = `Analyzing ${processedCount + 1} of ${totalCandidates}: ${candidate.name}`;

                    // Perform AI analysis
                    const analysisResult = await this.performAIAnalysis(candidate, this.currentJob);

                    // Save result
                    await this.databaseService.saveAnalysisResult(analysisResult);

                    processedCount++;

                } catch (error) {
                    console.error(`❌ Failed to analyze candidate ${candidate.name}:`, error);
                }
            }

            // Complete
            progressBar.style.width = '100%';
            statusDiv.textContent = `✅ Analysis complete: ${processedCount} candidates processed`;

            // Refresh results
            await this.loadAnalysisResults();
            this.renderATSResults();
            this.updateDashboardStats();

            setTimeout(() => {
                progressContainer.style.display = 'none';
            }, 3000);

            this.showSuccess(`AI analysis completed for ${processedCount} candidates`);

        } catch (error) {
            console.error('❌ Bulk ATS analysis failed:', error);
            this.showError('Bulk analysis failed: ' + error.message);
            progressContainer.style.display = 'none';
        }
    }

    async analyzeSelectedCandidates() {
        const progressContainer = document.getElementById('analysisProgress');
        const progressBar = document.getElementById('analysisProgressBar');
        const statusDiv = document.getElementById('analysisStatus');

        progressContainer.style.display = 'block';

        try {
            const selectedCandidateObjects = this.candidates.filter(c => this.selectedCandidates.includes(c.id));
            const totalCandidates = selectedCandidateObjects.length;
            let processedCount = 0;

            for (const candidate of selectedCandidateObjects) {
                try {
                    // Update progress
                    const progress = (processedCount / totalCandidates) * 100;
                    progressBar.style.width = `${progress}%`;
                    statusDiv.textContent = `Analyzing ${processedCount + 1} of ${totalCandidates}: ${candidate.name}`;

                    // Perform AI analysis
                    const analysisResult = await this.performAIAnalysis(candidate, this.currentJob);

                    // Save result
                    await this.databaseService.saveAnalysisResult(analysisResult);

                    processedCount++;

                } catch (error) {
                    console.error(`❌ Failed to analyze candidate ${candidate.name}:`, error);
                }
            }

            // Complete
            progressBar.style.width = '100%';
            statusDiv.textContent = `✅ Analysis complete: ${processedCount} candidates processed`;

            // Refresh results
            await this.loadAnalysisResults();
            this.renderATSResults();
            this.updateDashboardStats();

            setTimeout(() => {
                progressContainer.style.display = 'none';
            }, 3000);

            this.showSuccess(`AI analysis completed for ${processedCount} selected candidates`);

        } catch (error) {
            console.error('❌ Selected candidates analysis failed:', error);
            this.showError('Analysis failed: ' + error.message);
            progressContainer.style.display = 'none';
        }
    }

    async performAIAnalysis(candidate, job) {
        // Advanced AI scoring algorithm from HR perspective
        const analysis = {
            candidateId: candidate.id,
            jobId: job.id,
            candidateInfo: candidate,
            jobInfo: job,
            timestamp: new Date().toISOString(),
            scores: {},
            recommendations: [],
            hrNotes: []
        };

        // 1. Skills Match Analysis (40% weight)
        const skillsAnalysis = this.analyzeSkillsMatch(candidate, job);
        analysis.scores.skillsMatch = skillsAnalysis.score;
        analysis.skillsMatched = skillsAnalysis.matched;
        analysis.skillsMissing = skillsAnalysis.missing;

        // 2. Experience Analysis (25% weight)
        const experienceAnalysis = this.analyzeExperience(candidate, job);
        analysis.scores.experience = experienceAnalysis.score;
        analysis.experienceGap = experienceAnalysis.gap;

        // 3. AI Keywords Analysis (20% weight)
        const keywordAnalysis = this.analyzeAIKeywords(candidate, job);
        analysis.scores.aiKeywords = keywordAnalysis.score;
        analysis.keywordsFound = keywordAnalysis.found;

        // 4. Education & Qualifications (10% weight)
        const educationAnalysis = this.analyzeEducation(candidate, job);
        analysis.scores.education = educationAnalysis.score;

        // 5. Cultural Fit & Soft Skills (5% weight)
        const culturalAnalysis = this.analyzeCulturalFit(candidate, job);
        analysis.scores.culturalFit = culturalAnalysis.score;

        // Calculate overall AI score
        analysis.overallScore = (
            analysis.scores.skillsMatch * 0.40 +
            analysis.scores.experience * 0.25 +
            analysis.scores.aiKeywords * 0.20 +
            analysis.scores.education * 0.10 +
            analysis.scores.culturalFit * 0.05
        );

        // Generate HR recommendations
        analysis.recommendations = this.generateHRRecommendations(analysis);
        analysis.hrNotes = this.generateHRNotes(analysis);

        // Determine AI category
        analysis.aiCategory = this.determineAICategory(analysis.overallScore);

        return analysis;
    }

    // ===== AI ANALYSIS HELPER METHODS =====

    analyzeSkillsMatch(candidate, job) {
        const candidateSkills = candidate.skills.map(s => s.toLowerCase());
        const requiredSkills = job.requiredSkills.map(s => s.toLowerCase());

        const matched = requiredSkills.filter(skill =>
            candidateSkills.some(cSkill => cSkill.includes(skill) || skill.includes(cSkill))
        );

        const missing = requiredSkills.filter(skill =>
            !candidateSkills.some(cSkill => cSkill.includes(skill) || skill.includes(cSkill))
        );

        const score = requiredSkills.length > 0 ? (matched.length / requiredSkills.length) * 100 : 0;

        return { score, matched, missing };
    }

    analyzeExperience(candidate, job) {
        const candidateExp = candidate.experienceYears || 0;
        const requiredExpRange = job.experienceRequired;

        let minRequired = 0;
        let maxRequired = 50;

        // Parse experience requirement
        if (requiredExpRange.includes('-')) {
            const [min, max] = requiredExpRange.split('-').map(x => parseInt(x.replace('+', '')));
            minRequired = min;
            maxRequired = max || 50;
        } else if (requiredExpRange.includes('+')) {
            minRequired = parseInt(requiredExpRange.replace('+', ''));
            maxRequired = 50;
        }

        let score = 0;
        let gap = 0;

        if (candidateExp >= minRequired && candidateExp <= maxRequired) {
            score = 100; // Perfect match
        } else if (candidateExp < minRequired) {
            gap = minRequired - candidateExp;
            score = Math.max(0, 100 - (gap * 20)); // Penalize 20 points per year short
        } else {
            // Over-qualified
            const excess = candidateExp - maxRequired;
            score = Math.max(70, 100 - (excess * 5)); // Slight penalty for over-qualification
        }

        return { score, gap };
    }

    analyzeAIKeywords(candidate, job) {
        const resumeText = candidate.resumeText.toLowerCase();
        const aiKeywords = job.atsKeywords.map(k => k.toLowerCase());

        const found = aiKeywords.filter(keyword => resumeText.includes(keyword));
        const score = aiKeywords.length > 0 ? (found.length / aiKeywords.length) * 100 : 100;

        return { score, found };
    }

    analyzeEducation(candidate, job) {
        const education = candidate.education || '';
        const educationLower = education.toLowerCase();

        // Basic education scoring
        let score = 50; // Base score

        if (educationLower.includes('master') || educationLower.includes('mtech') || educationLower.includes('mca')) {
            score += 30;
        } else if (educationLower.includes('bachelor') || educationLower.includes('btech') || educationLower.includes('be')) {
            score += 20;
        }

        if (educationLower.includes('computer') || educationLower.includes('software') || educationLower.includes('it')) {
            score += 20;
        }

        return { score: Math.min(100, score) };
    }

    analyzeCulturalFit(candidate, job) {
        // Basic cultural fit analysis based on resume content
        const resumeText = candidate.resumeText.toLowerCase();
        let score = 70; // Base score

        // Look for teamwork indicators
        if (resumeText.includes('team') || resumeText.includes('collaboration') || resumeText.includes('agile')) {
            score += 15;
        }

        // Look for leadership indicators
        if (resumeText.includes('lead') || resumeText.includes('mentor') || resumeText.includes('manage')) {
            score += 10;
        }

        // Look for communication skills
        if (resumeText.includes('communication') || resumeText.includes('presentation') || resumeText.includes('client')) {
            score += 5;
        }

        return { score: Math.min(100, score) };
    }

    generateHRRecommendations(analysis) {
        const recommendations = [];

        // Skills-based recommendations
        if (analysis.scores.skillsMatch < 60) {
            recommendations.push({
                type: 'skills_gap',
                priority: 'high',
                message: `Candidate lacks ${analysis.skillsMissing.length} key skills: ${analysis.skillsMissing.join(', ')}`,
                action: 'Consider skills training or look for candidates with stronger technical background'
            });
        } else if (analysis.scores.skillsMatch < 80) {
            recommendations.push({
                type: 'skills_development',
                priority: 'medium',
                message: `Good skills match but missing: ${analysis.skillsMissing.join(', ')}`,
                action: 'Candidate could be trained in missing skills during onboarding'
            });
        }

        // Experience-based recommendations
        if (analysis.experienceGap > 0) {
            recommendations.push({
                type: 'experience_gap',
                priority: analysis.experienceGap > 2 ? 'high' : 'medium',
                message: `Candidate has ${analysis.experienceGap} years less experience than required`,
                action: 'Consider junior role or provide additional mentoring support'
            });
        }

        // ATS optimization recommendations
        if (analysis.scores.atsKeywords < 70) {
            recommendations.push({
                type: 'ats_optimization',
                priority: 'low',
                message: 'Resume could be better optimized for ATS systems',
                action: 'Suggest resume improvements if candidate progresses to interview stage'
            });
        }

        // Overall recommendations
        if (analysis.overallScore >= 85) {
            recommendations.push({
                type: 'strong_candidate',
                priority: 'high',
                message: 'Excellent candidate match for this role',
                action: 'Fast-track to interview process'
            });
        } else if (analysis.overallScore >= 70) {
            recommendations.push({
                type: 'good_candidate',
                priority: 'medium',
                message: 'Good candidate with potential',
                action: 'Schedule phone screening to assess further'
            });
        } else if (analysis.overallScore >= 50) {
            recommendations.push({
                type: 'marginal_candidate',
                priority: 'low',
                message: 'Marginal fit for current role',
                action: 'Consider for future opportunities or different roles'
            });
        } else {
            recommendations.push({
                type: 'poor_fit',
                priority: 'low',
                message: 'Poor fit for this role',
                action: 'Politely decline and keep in talent pool for other opportunities'
            });
        }

        return recommendations;
    }

    generateHRNotes(analysis) {
        const notes = [];

        notes.push(`Overall AI Score: ${analysis.overallScore.toFixed(1)}% - ${this.determineAICategory(analysis.overallScore)}`);
        notes.push(`Skills Match: ${analysis.scores.skillsMatch.toFixed(1)}% (${analysis.skillsMatched.length}/${analysis.skillsMatched.length + analysis.skillsMissing.length} skills matched)`);
        notes.push(`Experience Level: ${analysis.scores.experience.toFixed(1)}% match for required experience`);
        notes.push(`AI Keywords: ${analysis.scores.aiKeywords.toFixed(1)}% (${analysis.keywordsFound.length} keywords found)`);

        if (analysis.skillsMatched.length > 0) {
            notes.push(`Strong Skills: ${analysis.skillsMatched.join(', ')}`);
        }

        if (analysis.skillsMissing.length > 0) {
            notes.push(`Missing Skills: ${analysis.skillsMissing.join(', ')}`);
        }

        return notes;
    }

    determineAICategory(score) {
        if (score >= 85) return 'Excellent Match';
        if (score >= 70) return 'Good Match';
        if (score >= 50) return 'Average Match';
        return 'Poor Match';
    }

    getScoreClass(score) {
        if (score >= 85) return 'score-excellent';
        if (score >= 70) return 'score-good';
        if (score >= 50) return 'score-average';
        return 'score-poor';
    }

    renderATSResults() {
        const container = document.getElementById('atsResults');

        if (this.analysisResults.length === 0) {
            container.innerHTML = `
                <div class="text-center py-4">
                    <i class="bi bi-graph-up display-4 text-muted"></i>
                    <h6 class="mt-3 text-muted">No analysis results yet</h6>
                    <p class="text-muted">Select a job role and analyze candidates to see ATS scores</p>
                </div>
            `;
            return;
        }

        // Sort by overall score (highest first)
        const sortedResults = [...this.analysisResults].sort((a, b) => (b.overallScore || 0) - (a.overallScore || 0));

        container.innerHTML = `
            <div class="row mb-3">
                <div class="col-12">
                    <h6>Analysis Results (${sortedResults.length} candidates)</h6>
                </div>
            </div>
            ${sortedResults.map(result => this.renderATSResultCard(result)).join('')}
        `;
    }

    renderATSResultCard(result) {
        const scoreClass = this.getScoreClass(result.overallScore);
        const priorityRecommendation = result.recommendations.find(r => r.priority === 'high') || result.recommendations[0];

        return `
            <div class="card mb-3">
                <div class="card-body">
                    <div class="row align-items-center">
                        <div class="col-md-3">
                            <h6 class="mb-1">${result.candidateInfo.name}</h6>
                            <small class="text-muted">${result.candidateInfo.position}</small>
                        </div>
                        <div class="col-md-2 text-center">
                            <div class="ai-score ${scoreClass}">${result.overallScore.toFixed(1)}%</div>
                            <small class="text-muted">${result.aiCategory}</small>
                        </div>
                        <div class="col-md-3">
                            <div class="mb-1">
                                <small>Skills: <span class="text-success">${result.scores.skillsMatch.toFixed(0)}%</span></small>
                            </div>
                            <div class="mb-1">
                                <small>Experience: <span class="text-info">${result.scores.experience.toFixed(0)}%</span></small>
                            </div>
                        </div>
                        <div class="col-md-3">
                            ${priorityRecommendation ? `
                                <span class="badge bg-${priorityRecommendation.priority === 'high' ? 'danger' : priorityRecommendation.priority === 'medium' ? 'warning' : 'secondary'} mb-1">
                                    ${priorityRecommendation.type.replace('_', ' ').toUpperCase()}
                                </span>
                                <br>
                                <small class="text-muted">${priorityRecommendation.message}</small>
                            ` : ''}
                        </div>
                        <div class="col-md-1">
                            <button class="btn btn-sm btn-outline-primary" onclick="hrDashboard.viewDetailedAnalysis('${result.candidateId}')">
                                <i class="bi bi-eye"></i>
                            </button>
                        </div>
                    </div>

                    <div class="mt-3">
                        <div class="row">
                            <div class="col-md-6">
                                <small class="text-success">✓ Matched Skills: ${result.skillsMatched.join(', ')}</small>
                            </div>
                            <div class="col-md-6">
                                <small class="text-danger">✗ Missing Skills: ${result.skillsMissing.join(', ')}</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `;
    }

    // ===== UTILITY METHODS =====

    async refreshCVDatabase() {
        await this.loadCandidates();
        this.renderCVDatabase();
        this.updateDashboardStats();
        this.showSuccess('CV database refreshed');
    }

    filterCVDatabase() {
        const searchTerm = document.getElementById('cvSearchInput').value.toLowerCase();
        const experienceFilter = document.getElementById('experienceFilter').value;
        const skillsFilter = document.getElementById('skillsFilter').value;

        let filteredCandidates = [...this.candidates];

        // Apply search filter
        if (searchTerm) {
            filteredCandidates = filteredCandidates.filter(candidate =>
                candidate.name.toLowerCase().includes(searchTerm) ||
                candidate.email.toLowerCase().includes(searchTerm) ||
                candidate.position.toLowerCase().includes(searchTerm) ||
                candidate.skills.some(skill => skill.toLowerCase().includes(searchTerm))
            );
        }

        // Apply experience filter
        if (experienceFilter) {
            filteredCandidates = filteredCandidates.filter(candidate => {
                const exp = candidate.experienceYears;
                switch (experienceFilter) {
                    case '0-2': return exp >= 0 && exp <= 2;
                    case '3-5': return exp >= 3 && exp <= 5;
                    case '6-10': return exp >= 6 && exp <= 10;
                    case '10+': return exp > 10;
                    default: return true;
                }
            });
        }

        // Apply skills filter
        if (skillsFilter) {
            filteredCandidates = filteredCandidates.filter(candidate =>
                candidate.skills.some(skill => skill.toLowerCase().includes(skillsFilter.toLowerCase()))
            );
        }

        // Temporarily update candidates for rendering
        const originalCandidates = this.candidates;
        this.candidates = filteredCandidates;
        this.renderCVDatabase();
        this.candidates = originalCandidates;
    }

    async viewCandidate(candidateId) {
        const candidate = this.candidates.find(c => c.id === candidateId);
        if (!candidate) return;

        // Create modal to show candidate details
        const modalHtml = `
            <div class="modal fade" id="candidateModal" tabindex="-1">
                <div class="modal-dialog modal-lg">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title">Candidate Details</h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                        </div>
                        <div class="modal-body">
                            <div class="row">
                                <div class="col-md-6">
                                    <h6>Personal Information</h6>
                                    <p><strong>Name:</strong> ${candidate.name}</p>
                                    <p><strong>Email:</strong> ${candidate.email}</p>
                                    <p><strong>Phone:</strong> ${candidate.phone}</p>
                                    <p><strong>Position:</strong> ${candidate.position}</p>
                                    <p><strong>Experience:</strong> ${candidate.experienceYears} years</p>
                                    <p><strong>Location:</strong> ${candidate.location}</p>
                                </div>
                                <div class="col-md-6">
                                    <h6>Skills</h6>
                                    <div class="mb-3">
                                        ${candidate.skills.map(skill => `<span class="badge bg-primary me-1 mb-1">${skill}</span>`).join('')}
                                    </div>
                                    <h6>Education</h6>
                                    <p>${candidate.education}</p>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-12">
                                    <h6>Resume Content</h6>
                                    <div class="border p-3" style="max-height: 300px; overflow-y: auto; background: #f8f9fa;">
                                        <pre style="white-space: pre-wrap; font-size: 0.9em;">${candidate.resumeText}</pre>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                            <button type="button" class="btn btn-primary" onclick="hrDashboard.scheduleInterview('${candidateId}')">Schedule Interview</button>
                        </div>
                    </div>
                </div>
            </div>
        `;

        // Remove existing modal if any
        const existingModal = document.getElementById('candidateModal');
        if (existingModal) existingModal.remove();

        // Add modal to page
        document.body.insertAdjacentHTML('beforeend', modalHtml);

        // Show modal
        const modal = new bootstrap.Modal(document.getElementById('candidateModal'));
        modal.show();
    }

    async viewDetailedAnalysis(candidateId) {
        const analysis = this.analysisResults.find(r => r.candidateId === candidateId);
        if (!analysis) return;

        // Create detailed analysis modal
        const modalHtml = `
            <div class="modal fade" id="analysisModal" tabindex="-1">
                <div class="modal-dialog modal-xl">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title">Detailed AI Analysis - ${analysis.candidateInfo.name}</h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                        </div>
                        <div class="modal-body">
                            <div class="row mb-4">
                                <div class="col-md-3 text-center">
                                    <div class="ai-score ${this.getScoreClass(analysis.overallScore)} display-6">
                                        ${analysis.overallScore.toFixed(1)}%
                                    </div>
                                    <p class="mb-0">${analysis.aiCategory}</p>
                                </div>
                                <div class="col-md-9">
                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="mb-2">
                                                <strong>Skills Match:</strong>
                                                <span class="badge bg-primary">${analysis.scores.skillsMatch.toFixed(1)}%</span>
                                            </div>
                                            <div class="mb-2">
                                                <strong>Experience:</strong>
                                                <span class="badge bg-info">${analysis.scores.experience.toFixed(1)}%</span>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="mb-2">
                                                <strong>ATS Keywords:</strong>
                                                <span class="badge bg-warning">${analysis.scores.atsKeywords.toFixed(1)}%</span>
                                            </div>
                                            <div class="mb-2">
                                                <strong>Education:</strong>
                                                <span class="badge bg-success">${analysis.scores.education.toFixed(1)}%</span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="row">
                                <div class="col-md-6">
                                    <div class="card">
                                        <div class="card-header">
                                            <h6>HR Recommendations</h6>
                                        </div>
                                        <div class="card-body">
                                            ${analysis.recommendations.map(rec => `
                                                <div class="alert alert-${rec.priority === 'high' ? 'danger' : rec.priority === 'medium' ? 'warning' : 'info'} py-2">
                                                    <strong>${rec.type.replace('_', ' ').toUpperCase()}:</strong><br>
                                                    ${rec.message}<br>
                                                    <small><em>Action: ${rec.action}</em></small>
                                                </div>
                                            `).join('')}
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="card">
                                        <div class="card-header">
                                            <h6>HR Notes</h6>
                                        </div>
                                        <div class="card-body">
                                            ${analysis.hrNotes.map(note => `<p class="mb-2">• ${note}</p>`).join('')}
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="row mt-3">
                                <div class="col-md-6">
                                    <div class="card">
                                        <div class="card-header bg-success text-white">
                                            <h6 class="mb-0">✓ Matched Skills</h6>
                                        </div>
                                        <div class="card-body">
                                            ${analysis.skillsMatched.map(skill => `<span class="badge bg-success me-1 mb-1">${skill}</span>`).join('')}
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="card">
                                        <div class="card-header bg-danger text-white">
                                            <h6 class="mb-0">✗ Missing Skills</h6>
                                        </div>
                                        <div class="card-body">
                                            ${analysis.skillsMissing.map(skill => `<span class="badge bg-danger me-1 mb-1">${skill}</span>`).join('')}
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                            <button type="button" class="btn btn-success" onclick="hrDashboard.scheduleInterview('${candidateId}')">Schedule Interview</button>
                            <button type="button" class="btn btn-primary" onclick="hrDashboard.exportAnalysis('${candidateId}')">Export Analysis</button>
                        </div>
                    </div>
                </div>
            </div>
        `;

        // Remove existing modal if any
        const existingModal = document.getElementById('analysisModal');
        if (existingModal) existingModal.remove();

        // Add modal to page
        document.body.insertAdjacentHTML('beforeend', modalHtml);

        // Show modal
        const modal = new bootstrap.Modal(document.getElementById('analysisModal'));
        modal.show();
    }

    // ===== EXPORT AND REPORTING METHODS =====

    async generateDetailedHRReport() {
        try {
            const reportData = {
                generatedAt: new Date().toISOString(),
                totalCandidates: this.candidates.length,
                totalJobs: this.jobs.length,
                totalAnalyses: this.analysisResults.length,
                averageScore: this.analysisResults.length > 0
                    ? this.analysisResults.reduce((sum, r) => sum + (r.overallScore || 0), 0) / this.analysisResults.length
                    : 0,
                topCandidates: this.analysisResults
                    .sort((a, b) => (b.overallScore || 0) - (a.overallScore || 0))
                    .slice(0, 10),
                skillsAnalysis: this.analyzeSkillsDistribution(),
                departmentAnalysis: this.analyzeDepartmentDistribution(),
                candidates: this.candidates,
                jobs: this.jobs,
                analysisResults: this.analysisResults
            };

            const reportHtml = this.createDetailedHRReport(reportData);
            this.downloadReport(reportHtml, 'CubeAI_HR_Detailed_Report.html');

        } catch (error) {
            console.error('❌ Report generation failed:', error);
            this.showError('Failed to generate report: ' + error.message);
        }
    }

    analyzeSkillsDistribution() {
        const skillsCount = {};

        this.candidates.forEach(candidate => {
            candidate.skills.forEach(skill => {
                skillsCount[skill] = (skillsCount[skill] || 0) + 1;
            });
        });

        return Object.entries(skillsCount)
            .sort(([,a], [,b]) => b - a)
            .slice(0, 15);
    }

    analyzeDepartmentDistribution() {
        const deptCount = {};

        this.jobs.forEach(job => {
            deptCount[job.department] = (deptCount[job.department] || 0) + 1;
        });

        return Object.entries(deptCount);
    }

    // ===== NOTIFICATION METHODS =====

    showSuccess(message) {
        this.showNotification(message, 'success');
    }

    showError(message) {
        this.showNotification(message, 'danger');
    }

    showNotification(message, type = 'info') {
        const alertDiv = document.createElement('div');
        alertDiv.className = `alert alert-${type} alert-dismissible fade show position-fixed`;
        alertDiv.style.cssText = 'top: 20px; right: 20px; z-index: 9999; max-width: 400px;';
        alertDiv.innerHTML = `
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;

        document.body.appendChild(alertDiv);

        // Auto-remove after 5 seconds
        setTimeout(() => {
            if (alertDiv.parentNode) {
                alertDiv.parentNode.removeChild(alertDiv);
            }
        }, 5000);
    }

    downloadReport(content, filename) {
        const blob = new Blob([content], { type: 'text/html' });
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = filename;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        URL.revokeObjectURL(url);

        this.showSuccess('Report downloaded successfully');
    }
}

// Initialize HR AI Automation Agent when page loads
let hrDashboard;
document.addEventListener('DOMContentLoaded', () => {
    hrDashboard = new CubeAIHRAutomationAgent();
});

/**
 * CubeAI Solutions - Demo Data for AI Resume Analyzer
 * Comprehensive sample data for testing and demonstration
 */

const DEMO_DATA = {
    // Sample Job Descriptions for different roles
    jobDescriptions: {
        fullStackDeveloper: `Senior Full Stack Developer - Remote

We are seeking an experienced Full Stack Developer to join our dynamic team. The ideal candidate will have 5+ years of experience in web development and a strong background in modern technologies.

Required Skills:
• 5+ years of experience in JavaScript and Python
• Proficiency in React, Node.js, and Express.js
• Experience with MongoDB and PostgreSQL databases
• Knowledge of AWS cloud services
• Familiarity with Docker and CI/CD pipelines
• Strong understanding of RESTful APIs and microservices architecture

Education:
• Bachelor's degree in Computer Science or related field

Responsibilities:
• Develop and maintain scalable web applications
• Collaborate with cross-functional teams
• Implement best practices for code quality and testing
• Participate in code reviews and technical discussions

Benefits:
• Competitive salary ($120,000 - $150,000)
• Remote work flexibility
• Health insurance and 401k
• Professional development opportunities`,

        dataScientist: `Senior Data Scientist - AI/ML Focus

Join our AI team to build cutting-edge machine learning solutions that impact millions of users worldwide.

Required Skills:
• 4+ years of experience in Python and R
• Expertise in TensorFlow, PyTorch, and Scikit-learn
• Strong background in statistics and machine learning algorithms
• Experience with big data tools (Spark, Hadoop)
• Proficiency in SQL and NoSQL databases
• Knowledge of cloud platforms (AWS, GCP, Azure)

Education:
• Master's degree in Data Science, Statistics, or related field
• PhD preferred

Responsibilities:
• Design and implement ML models for production
• Analyze large datasets to extract business insights
• Collaborate with engineering teams on model deployment
• Research and implement state-of-the-art algorithms

Requirements:
• Strong mathematical and statistical background
• Experience with A/B testing and experimental design
• Excellent communication skills`,

        frontendDeveloper: `Frontend Developer - React Specialist

We're looking for a passionate Frontend Developer to create amazing user experiences with modern web technologies.

Required Skills:
• 3+ years of experience with React and JavaScript
• Proficiency in HTML5, CSS3, and responsive design
• Experience with state management (Redux, Context API)
• Knowledge of modern build tools (Webpack, Vite)
• Familiarity with TypeScript
• Understanding of web performance optimization

Nice to Have:
• Experience with Next.js or Gatsby
• Knowledge of testing frameworks (Jest, Cypress)
• Familiarity with design systems
• Basic understanding of backend technologies

Education:
• Bachelor's degree in Computer Science or equivalent experience

Responsibilities:
• Build responsive and interactive web applications
• Collaborate with designers and backend developers
• Optimize applications for maximum speed and scalability
• Maintain code quality and best practices`
    },

    // Sample Resume Templates
    resumeTemplates: {
        seniorDeveloper: `Rajesh Kumar Murugan
Senior Software Engineer
Email: <EMAIL> | Phone: +91-98765-43210
LinkedIn: linkedin.com/in/rajeshmurugan | GitHub: github.com/rajeshmurugan

PROFESSIONAL SUMMARY
Experienced Full Stack Developer with 7+ years in web development, specializing in React, Node.js, and cloud technologies. Proven track record of delivering scalable applications serving millions of users and leading development teams of 5+ engineers across Chennai and Bangalore tech hubs.

TECHNICAL SKILLS
• Programming Languages: JavaScript, TypeScript, Python, Java, Go
• Frontend: React, Vue.js, Angular, HTML5, CSS3, SASS, Bootstrap, Tailwind CSS
• Backend: Node.js, Express.js, Django, Spring Boot, FastAPI
• Databases: MongoDB, PostgreSQL, MySQL, Redis, Elasticsearch
• Cloud & DevOps: AWS (EC2, S3, Lambda, RDS), Docker, Kubernetes, Jenkins, GitLab CI
• Tools & Technologies: Git, JIRA, VS Code, Postman, Figma

WORK EXPERIENCE
Senior Software Engineer | Zoho Corporation | 2021 - Present
• Led development of microservices architecture serving 2M+ daily active users
• Implemented CI/CD pipelines reducing deployment time by 75% and increasing release frequency
• Mentored 5 junior developers and conducted technical interviews
• Architected real-time chat system handling 100K+ concurrent connections
• Reduced application load time by 60% through performance optimization

Software Engineer | Freshworks Inc. | 2019 - 2021
• Built responsive web applications using React and Node.js for e-commerce platform
• Optimized database queries improving API response time by 45%
• Collaborated with cross-functional teams in Agile environment
• Implemented automated testing increasing code coverage from 40% to 85%

Junior Developer | Tata Consultancy Services | 2017 - 2019
• Developed and maintained client websites using modern web technologies
• Participated in code reviews and followed best practices
• Worked on bug fixes and feature enhancements

EDUCATION
Bachelor of Engineering in Computer Science
Anna University, Chennai | 2013 - 2017
CGPA: 8.5/10.0
Relevant Coursework: Data Structures, Algorithms, Database Systems, Software Engineering

CERTIFICATIONS
• AWS Certified Solutions Architect - Associate (2023)
• Google Cloud Professional Developer (2022)
• Certified Scrum Master (2021)
• MongoDB Certified Developer (2020)

PROJECTS
E-Commerce Platform | 2022
• Built full-stack e-commerce application using React, Node.js, and MongoDB
• Implemented payment integration with Razorpay API
• Deployed on AWS with auto-scaling capabilities
• GitHub: github.com/rajeshmurugan/ecommerce-platform

Real-Time Analytics Dashboard | 2021
• Created real-time data visualization dashboard using D3.js and WebSocket
• Processed and displayed live data from multiple APIs
• Implemented responsive design for mobile and desktop

ACHIEVEMENTS
• Increased team productivity by 40% through implementation of automated workflows
• Reduced customer support tickets by 30% through improved UI/UX design
• Speaker at TechConf Chennai 2022: "Building Scalable Microservices"
• Open source contributor with 500+ GitHub stars across projects`,

        dataScientistResume: `Dr. Priya Lakshmi Sundaram
Senior Data Scientist
Email: <EMAIL> | Phone: +91-98765-54321
LinkedIn: linkedin.com/in/priyasundaram | GitHub: github.com/priyasundaram

PROFESSIONAL SUMMARY
Data Scientist with 6+ years of experience in machine learning, statistical analysis, and big data processing. Expert in Python, R, and cloud-based analytics platforms with a proven track record of building ML models that drive business value and improve customer experience across Indian and global markets.

TECHNICAL SKILLS
• Programming: Python, R, SQL, Scala, Java
• Machine Learning: TensorFlow, PyTorch, Scikit-learn, Keras, XGBoost, LightGBM
• Data Processing: Pandas, NumPy, Apache Spark, Hadoop, Dask
• Visualization: Matplotlib, Seaborn, Plotly, Tableau, Power BI, D3.js
• Cloud Platforms: AWS (SageMaker, EMR, Redshift), GCP (BigQuery, Vertex AI), Azure ML
• Databases: PostgreSQL, MongoDB, Cassandra, Snowflake, BigQuery
• Tools: Jupyter, Git, Docker, Kubernetes, Airflow, MLflow

WORK EXPERIENCE
Senior Data Scientist | Flipkart Labs | 2022 - Present
• Developed predictive models improving customer retention by 35% and reducing churn by ₹15 Cr annually
• Built real-time recommendation systems using collaborative filtering and deep learning
• Led data science team of 6 members on multiple high-impact projects
• Implemented MLOps pipelines reducing model deployment time from weeks to hours
• Created automated feature engineering pipeline processing 10TB+ daily data

Data Scientist | Ola Cabs | 2020 - 2022
• Performed statistical analysis on large datasets (50M+ records) to identify business insights
• Created automated reporting dashboards reducing manual work by 80%
• Collaborated with business stakeholders to define KPIs and success metrics
• Built time series forecasting models with 95% accuracy for demand planning
• Implemented A/B testing framework used across 20+ product teams

Junior Data Analyst | Infosys Limited | 2018 - 2020
• Analyzed customer behavior data to identify growth opportunities
• Created data visualizations and reports for executive leadership
• Performed statistical tests and hypothesis validation
• Assisted in building ETL pipelines for data warehouse

EDUCATION
Ph.D. in Statistics | Indian Institute of Science, Bangalore | 2016 - 2018
• Dissertation: "Advanced Bayesian Methods for High-Dimensional Data Analysis"
• Published 8 peer-reviewed papers in top-tier journals

Master of Science in Data Science | Indian Institute of Technology, Madras | 2014 - 2016
• CGPA: 9.2/10.0
• Thesis: "Deep Learning Applications in Natural Language Processing"

Bachelor of Science in Mathematics | Stella Maris College, Chennai | 2011 - 2014
• First Class with Distinction, CGPA: 8.8/10.0

CERTIFICATIONS
• Google Cloud Professional Data Engineer (2023)
• Microsoft Azure Data Scientist Associate (2022)
• AWS Certified Machine Learning - Specialty (2021)
• Tableau Desktop Specialist (2020)

PUBLICATIONS & RESEARCH
• "Scalable Machine Learning for Real-Time Recommendations" - KDD 2023
• "Bayesian Optimization for Hyperparameter Tuning" - ICML 2022
• "Deep Learning for Time Series Forecasting" - NeurIPS 2021
• 15+ publications in peer-reviewed journals with 500+ citations

PROJECTS
Customer Churn Prediction System | 2023
• Built end-to-end ML pipeline predicting customer churn with 92% accuracy
• Deployed model serving 1M+ predictions daily with <100ms latency
• Integrated with business systems for real-time intervention

Fraud Detection Engine | 2022
• Developed real-time fraud detection system using ensemble methods
• Reduced false positives by 60% while maintaining 99.5% recall
• Processed 100K+ transactions per minute

ACHIEVEMENTS
• Reduced customer acquisition cost by 25% through predictive modeling
• Increased revenue by ₹35 Cr through personalization algorithms
• Keynote speaker at DataCon Bangalore 2023: "The Future of ML in Production"
• Mentor for 10+ junior data scientists and interns`,

        juniorDeveloper: `Arun Kumar Selvam
Junior Frontend Developer
Email: <EMAIL> | Phone: +91-98765-67890
LinkedIn: linkedin.com/in/arunselvam | Portfolio: arunselvam.dev

PROFESSIONAL SUMMARY
Passionate Junior Frontend Developer with 2 years of experience building responsive web applications. Strong foundation in React, JavaScript, and modern web technologies with a keen eye for user experience and design. Eager to contribute to innovative projects in the growing Indian tech ecosystem.

TECHNICAL SKILLS
• Programming Languages: JavaScript, TypeScript, HTML5, CSS3
• Frontend Frameworks: React, Vue.js, Angular (basic)
• Styling: CSS3, SASS, Bootstrap, Tailwind CSS, Styled Components
• Tools: Git, VS Code, Figma, Chrome DevTools
• Build Tools: Webpack, Vite, npm, yarn
• Testing: Jest, React Testing Library
• Version Control: Git, GitHub

WORK EXPERIENCE
Frontend Developer | Byju's | 2022 - Present
• Developed responsive web applications using React and TypeScript
• Collaborated with designers to implement pixel-perfect UI components
• Improved website performance by 40% through code optimization
• Participated in code reviews and followed Agile development practices

Web Development Intern | Paytm | Summer 2021
• Built landing pages and marketing websites using HTML, CSS, and JavaScript
• Assisted senior developers with bug fixes and feature implementations
• Learned modern development workflows and best practices

EDUCATION
Bachelor of Engineering in Computer Science
SRM Institute of Science and Technology, Chennai | 2018 - 2022
CGPA: 8.2/10.0
Relevant Coursework: Web Development, Data Structures, Software Engineering

PROJECTS
Personal Portfolio Website | 2022
• Built responsive portfolio using React and Gatsby
• Implemented dark/light theme toggle and smooth animations
• Deployed on Netlify with continuous deployment from GitHub

Todo App with React | 2021
• Created full-featured todo application with local storage
• Implemented drag-and-drop functionality and filtering
• Used React Hooks and Context API for state management

CERTIFICATIONS
• freeCodeCamp Responsive Web Design (2021)
• JavaScript Algorithms and Data Structures (2021)

ACHIEVEMENTS
• University Rank Holder for 3 consecutive semesters
• Winner of SRM Hackathon 2021 - Best UI/UX Design
• Active contributor to open source projects`
    },

    // Industry-specific keywords and skills
    industryKeywords: {
        technology: [
            'software development', 'programming', 'coding', 'technical', 'IT', 'computer science',
            'algorithms', 'data structures', 'software engineering', 'web development',
            'mobile development', 'full stack', 'frontend', 'backend', 'DevOps'
        ],
        dataScience: [
            'machine learning', 'artificial intelligence', 'data analysis', 'statistics',
            'big data', 'analytics', 'data mining', 'predictive modeling', 'deep learning',
            'neural networks', 'natural language processing', 'computer vision'
        ],
        cloudComputing: [
            'cloud computing', 'AWS', 'Azure', 'Google Cloud', 'serverless', 'microservices',
            'containerization', 'Docker', 'Kubernetes', 'infrastructure', 'scalability'
        ],
        cybersecurity: [
            'cybersecurity', 'information security', 'network security', 'penetration testing',
            'vulnerability assessment', 'encryption', 'firewall', 'security protocols'
        ]
    },

    // ATS optimization keywords
    atsKeywords: [
        'experience', 'skills', 'education', 'certification', 'project', 'team', 'leadership',
        'management', 'development', 'analysis', 'implementation', 'optimization', 'collaboration',
        'communication', 'problem-solving', 'innovation', 'results-driven', 'detail-oriented'
    ],

    // Skill categories and weights
    skillCategories: {
        'Programming Languages': {
            skills: ['JavaScript', 'Python', 'Java', 'C++', 'C#', 'PHP', 'Ruby', 'Go', 'Rust', 'TypeScript'],
            weight: 0.25
        },
        'Frontend Technologies': {
            skills: ['React', 'Angular', 'Vue.js', 'HTML5', 'CSS3', 'Bootstrap', 'Tailwind', 'jQuery'],
            weight: 0.20
        },
        'Backend Technologies': {
            skills: ['Node.js', 'Express.js', 'Django', 'Flask', 'Spring Boot', 'Laravel', 'Ruby on Rails'],
            weight: 0.20
        },
        'Databases': {
            skills: ['MongoDB', 'PostgreSQL', 'MySQL', 'Redis', 'Cassandra', 'Oracle', 'SQLite'],
            weight: 0.15
        },
        'Cloud & DevOps': {
            skills: ['AWS', 'Azure', 'GCP', 'Docker', 'Kubernetes', 'Jenkins', 'Git', 'CI/CD'],
            weight: 0.15
        },
        'Data Science & ML': {
            skills: ['Machine Learning', 'TensorFlow', 'PyTorch', 'Pandas', 'NumPy', 'Scikit-learn'],
            weight: 0.05
        }
    }
};

// Export for use in other modules
if (typeof module !== 'undefined' && module.exports) {
    module.exports = DEMO_DATA;
} else if (typeof window !== 'undefined') {
    window.DEMO_DATA = DEMO_DATA;
}

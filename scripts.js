/**
 * Cube AI Solutions - Recruitment Portal JavaScript
 * Complete Zoho Recruit Clone Implementation
 * Optimized for VS Code Development
 */

class CubeRecruitApp {
    constructor() {
        this.currentModule = 'dashboard';
        this.user = null;
        this.notifications = [];
        this.activities = [];
        this.searchTimeout = null;
        this.isDemoMode = true;
        
        // Initialize application
        this.init();
    }

    // ===== INITIALIZATION =====
    init() {
        console.log('🚀 Initializing Cube AI Recruit Portal...');
        
        this.showLoadingScreen();
        this.bindEvents();
        this.loadInitialData();
        this.setupKeyboardShortcuts();
        
        // Auto-hide loading screen
        setTimeout(() => {
            this.hideLoadingScreen();
            this.checkAutoLogin();
        }, 2500);
    }

    bindEvents() {
        // Login form
        const loginForm = document.getElementById('login-form');
        if (loginForm) {
            loginForm.addEventListener('submit', (e) => this.handleLogin(e));
        }

        // Navigation tabs
        document.querySelectorAll('.nav-tab').forEach(tab => {
            tab.addEventListener('click', (e) => this.handleNavigation(e));
        });

        // Search functionality
        const globalSearch = document.querySelector('.global-search');
        if (globalSearch) {
            globalSearch.addEventListener('input', (e) => this.handleGlobalSearch(e));
            globalSearch.addEventListener('keypress', (e) => {
                if (e.key === 'Enter') {
                    this.performSearch(e.target.value);
                }
            });
        }

        // Window events
        window.addEventListener('resize', () => this.handleResize());
        window.addEventListener('beforeunload', () => this.saveUserSession());

        // Form validation
        this.setupFormValidation();
        
        console.log('✅ Event listeners bound successfully');
    }

    setupKeyboardShortcuts() {
        document.addEventListener('keydown', (e) => {
            // Global shortcuts
            if (e.ctrlKey || e.metaKey) {
                switch (e.key) {
                    case 'k':
                        e.preventDefault();
                        document.querySelector('.global-search')?.focus();
                        break;
                    case '1':
                        e.preventDefault();
                        this.switchModule('dashboard');
                        break;
                    case '2':
                        e.preventDefault();
                        this.switchModule('candidates');
                        break;
                    case '3':
                        e.preventDefault();
                        this.switchModule('jobs');
                        break;
                    case '4':
                        e.preventDefault();
                        this.switchModule('interviews');
                        break;
                }
            }
            
            if (e.key === 'Escape') {
                this.closeAllModals();
            }
        });
    }

    // ===== AUTHENTICATION =====
    async handleLogin(event) {
        event.preventDefault();
        
        const form = event.target;
        const email = form.querySelector('#email').value.trim();
        const password = form.querySelector('#password').value;
        const remember = form.querySelector('#remember').checked;
        
        // Clear any previous errors
        this.clearFormErrors();
        
        // Show loading state
        this.setLoginLoading(true);
        
        try {
            // Validate inputs
            if (!this.validateLoginForm(email, password)) {
                return;
            }
            
            // Simulate API call
            await this.simulateLogin(email, password);
            
            // Create user session
            this.user = {
                email: email,
                name: this.getDisplayName(email),
                avatar: this.getRandomAvatar(),
                role: 'Administrator',
                loginTime: new Date().toISOString(),
                lastActivity: new Date().toISOString()
            };
            
            // Save session if remember me is checked
            if (remember) {
                localStorage.setItem('cubeRecruit_rememberMe', 'true');
                localStorage.setItem('cubeRecruit_user', JSON.stringify(this.user));
            }
            
            // Success feedback
            this.showSuccessToast('Welcome back! Redirecting to dashboard...');
            
            // Transition to main app
            setTimeout(() => {
                this.showMainApplication();
            }, 1000);
            
        } catch (error) {
            this.showErrorToast(error.message);
            this.markFieldAsInvalid('email');
            this.markFieldAsInvalid('password');
        } finally {
            this.setLoginLoading(false);
        }
    }

    async simulateLogin(email, password) {
        // Simulate network delay
        await this.delay(1500);
        
        // Demo credentials
        const validCredentials = [
            { email: '<EMAIL>', password: 'password123' },
            { email: '<EMAIL>', password: 'demo123' },
            { email: '<EMAIL>', password: 'test123' }
        ];
        
        // Check credentials
        const isValid = validCredentials.some(cred => 
            cred.email.toLowerCase() === email.toLowerCase() && 
            cred.password === password
        );
        
        if (!isValid) {
            throw new Error('Invalid email or password. Please try again.');
        }
        
        return true;
    }

    validateLoginForm(email, password) {
        let isValid = true;
        
        // Email validation
        if (!email) {
            this.showFieldError('email', 'Email address is required');
            isValid = false;
        } else if (!this.isValidEmail(email)) {
            this.showFieldError('email', 'Please enter a valid email address');
            isValid = false;
        }
        
        // Password validation
        if (!password) {
            this.showFieldError('password', 'Password is required');
            isValid = false;
        } else if (password.length < 6) {
            this.showFieldError('password', 'Password must be at least 6 characters');
            isValid = false;
        }
        
        return isValid;
    }

    setLoginLoading(loading) {
        const loginBtn = document.getElementById('login-btn');
        const btnText = loginBtn.querySelector('.btn-text');
        const btnLoading = loginBtn.querySelector('.btn-loading');
        
        if (loading) {
            btnText.classList.add('d-none');
            btnLoading.classList.remove('d-none');
            loginBtn.disabled = true;
        } else {
            btnText.classList.remove('d-none');
            btnLoading.classList.add('d-none');
            loginBtn.disabled = false;
        }
    }

    showMainApplication() {
        // Hide login page
        document.getElementById('login-page').classList.add('d-none');
        
        // Show main app
        document.getElementById('main-app').classList.remove('d-none');
        
        // Update user info in header
        this.updateUserInfo();
        
        // Load dashboard data
        this.loadDashboardData();
        
        // Start activity tracking
        this.startActivityTracking();
        
        console.log('✅ Main application loaded successfully');
    }

    checkAutoLogin() {
        const rememberMe = localStorage.getItem('cubeRecruit_rememberMe');
        const savedUser = localStorage.getItem('cubeRecruit_user');
        
        if (rememberMe === 'true' && savedUser) {
            try {
                this.user = JSON.parse(savedUser);
                this.showSuccessToast('Welcome back! Auto-login successful.');
                setTimeout(() => {
                    this.showMainApplication();
                }, 500);
            } catch (error) {
                console.error('Error parsing saved user data:', error);
                localStorage.removeItem('cubeRecruit_user');
                localStorage.removeItem('cubeRecruit_rememberMe');
            }
        }
    }

    logout() {
        const confirmLogout = confirm('Are you sure you want to sign out?');
        
        if (confirmLogout) {
            // Clear user data
            this.user = null;
            localStorage.removeItem('cubeRecruit_rememberMe');
            localStorage.removeItem('cubeRecruit_user');
            
            // Show logout message
            this.showInfoToast('You have been logged out successfully.');
            
            // Reset to login page
            setTimeout(() => {
                document.getElementById('main-app').classList.add('d-none');
                document.getElementById('login-page').classList.remove('d-none');
                
                // Reset login form
                document.getElementById('login-form').reset();
                this.clearFormErrors();
            }, 500);
        }
    }

    // ===== NAVIGATION =====
    handleNavigation(event) {
        event.preventDefault();
        
        const tab = event.currentTarget;
        const module = tab.getAttribute('data-module');
        
        if (module && module !== this.currentModule) {
            this.switchModule(module);
        }
    }

    switchModule(module) {
        // Update navigation tabs
        document.querySelectorAll('.nav-tab').forEach(tab => {
            tab.classList.remove('active');
        });
        
        const activeTab = document.querySelector(`[data-module="${module}"]`);
        if (activeTab) {
            activeTab.classList.add('active');
        }
        
        // Update module content
        document.querySelectorAll('.module-content').forEach(content => {
            content.classList.remove('active');
        });
        
        const moduleContent = document.getElementById(`${module}-module`);
        if (moduleContent) {
            moduleContent.classList.add('active');
        }
        
        // Update breadcrumb
        const breadcrumb = document.getElementById('current-module');
        if (breadcrumb) {
            breadcrumb.textContent = this.getModuleTitle(module);
        }
        
        // Update current module
        this.currentModule = module;
        
        // Load module data
        this.loadModuleData(module);
        
        // Update URL
        if (history.pushState) {
            history.pushState(null, null, `#${module}`);
        }
        
        console.log(`📱 Switched to ${module} module`);
    }

    getModuleTitle(module) {
        const titles = {
            dashboard: 'Dashboard',
            candidates: 'Candidates',
            jobs: 'Job Openings',
            contacts: 'Contacts',
            interviews: 'Interviews',
            analytics: 'Analytics',
            reports: 'Reports',
            setup: 'Setup'
        };
        return titles[module] || 'Dashboard';
    }

    // ===== DATA MANAGEMENT =====
    loadInitialData() {
        // Sample notifications
        this.notifications = [
            {
                id: 1,
                type: 'application',
                title: 'New candidate applied for Senior AI Engineer',
                message: 'John Smith has submitted his application',
                time: '2 minutes ago',
                read: false,
                icon: 'bi-person-check',
                iconColor: 'text-success',
                timestamp: new Date(Date.now() - 2 * 60 * 1000)
            },
            {
                id: 2,
                type: 'interview',
                title: 'Interview scheduled for tomorrow',
                message: 'Technical interview with Sarah Johnson at 2:00 PM',
                time: '1 hour ago',
                read: false,
                icon: 'bi-calendar-check',
                iconColor: 'text-primary',
                timestamp: new Date(Date.now() - 60 * 60 * 1000)
            },
            {
                id: 3,
                type: 'message',
                title: 'New message from hiring manager',
                message: 'Please review the candidate profiles for Frontend Developer role',
                time: '3 hours ago',
                read: true,
                icon: 'bi-envelope',
                iconColor: 'text-info',
                timestamp: new Date(Date.now() - 3 * 60 * 60 * 1000)
            },
            {
                id: 4,
                type: 'offer',
                title: 'Offer accepted by candidate',
                message: 'Mike Chen has accepted the offer for DevOps Engineer position',
                time: '5 hours ago',
                read: true,
                icon: 'bi-check-circle',
                iconColor: 'text-success',
                timestamp: new Date(Date.now() - 5 * 60 * 60 * 1000)
            }
        ];
        
        // Sample activities
        this.activities = [
            {
                id: 1,
                type: 'application',
                candidate: 'John Smith',
                position: 'Senior AI Engineer',
                action: 'submitted application',
                time: '2 minutes ago',
                avatar: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=40&h=40&fit=crop&crop=face',
                status: 'new'
            },
            {
                id: 2,
                type: 'interview',
                candidate: 'Sarah Johnson',
                position: 'Frontend Developer',
                action: 'interview scheduled',
                time: '1 hour ago',
                avatar: 'https://images.unsplash.com/photo-1494790108755-2616b612b287?w=40&h=40&fit=crop&crop=face',
                status: 'scheduled'
            },
            {
                id: 3,
                type: 'status_change',
                candidate: 'Mike Chen',
                position: 'DevOps Engineer',
                action: 'moved to final round',
                time: '3 hours ago',
                avatar: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=40&h=40&fit=crop&crop=face',
                status: 'final_round'
            },
            {
                id: 4,
                type: 'offer',
                candidate: 'Lisa Wang',
                position: 'Data Scientist',
                action: 'offer extended',
                time: '5 hours ago',
                avatar: 'https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=40&h=40&fit=crop&crop=face',
                status: 'offer_sent'
            }
        ];
        
        this.updateNotificationBadge();
        this.renderNotifications();
        
        console.log('📊 Initial data loaded');
    }

    loadDashboardData() {
        // Animate statistics
        this.animateStatistics();
        
        // Update activities
        this.renderActivityTimeline();
        
        // Update pipeline
        this.animatePipelineProgress();
        
        // Simulate real-time updates
        this.startRealTimeUpdates();
    }

    loadModuleData(module) {
        switch (module) {
            case 'dashboard':
                this.loadDashboardData();
                break;
            case 'candidates':
                this.loadCandidatesData();
                break;
            case 'jobs':
                this.loadJobsData();
                break;
            case 'interviews':
                this.loadInterviewsData();
                break;
            default:
                console.log(`📁 Loading ${module} module data...`);
        }
    }

    loadCandidatesData() {
        console.log('👥 Loading candidates data...');
        // Implementation would go here
    }

    loadJobsData() {
        console.log('💼 Loading jobs data...');
        // Implementation would go here
    }

    loadInterviewsData() {
        console.log('📅 Loading interviews data...');
        // Implementation would go here
    }

    // ===== UI UPDATES =====
    animateStatistics() {
        const statElements = document.querySelectorAll('.stat-number');
        
        statElements.forEach(element => {
            const target = parseInt(element.getAttribute('data-target'));
            const duration = 2000; // 2 seconds
            const steps = 60;
            const increment = target / steps;
            let current = 0;
            
            const timer = setInterval(() => {
                current += increment;
                if (current >= target) {
                    element.textContent = target;
                    clearInterval(timer);
                } else {
                    element.textContent = Math.floor(current);
                }
            }, duration / steps);
        });
    }

    renderActivityTimeline() {
        const timeline = document.getElementById('activity-timeline');
        if (!timeline) return;
        
        timeline.innerHTML = '';
        
        this.activities.slice(0, 5).forEach(activity => {
            const item = this.createActivityItem(activity);
            timeline.appendChild(item);
        });
    }

    createActivityItem(activity) {
        const div = document.createElement('div');
        div.className = 'activity-item';
        
        div.innerHTML = `
            <div class="activity-avatar">
                <img src="${activity.avatar}" alt="${activity.candidate}">
                <div class="status-indicator ${activity.status === 'new' ? 'online' : ''}"></div>
            </div>
            <div class="activity-info">
                <h5>${activity.candidate}</h5>
                <p><strong>${activity.action}</strong> for ${activity.position}</p>
                <span class="activity-time">${activity.time}</span>
            </div>
            <div class="activity-action">
                <button class="btn btn-outline-primary btn-sm" onclick="cubeApp.handleActivityAction('${activity.id}', '${activity.type}')">
                    ${this.getActivityActionText(activity.type)}
                </button>
            </div>
        `;
        
        return div;
    }

    getActivityActionText(type) {
        const actions = {
            application: 'Review',
            interview: 'View Details',
            status_change: 'Details',
            offer: 'View Offer'
        };
        return actions[type] || 'View';
    }

    animatePipelineProgress() {
        const progressBars = document.querySelectorAll('.progress-fill');
        
        progressBars.forEach((bar, index) => {
            const percentage = bar.getAttribute('data-percentage');
            
            setTimeout(() => {
                bar.style.width = `${percentage}%`;
            }, index * 200);
        });
    }

    renderNotifications() {
        const notificationList = document.getElementById('notification-list');
        if (!notificationList) return;
        
        notificationList.innerHTML = '';
        
        this.notifications.forEach(notification => {
            const item = this.createNotificationItem(notification);
            notificationList.appendChild(item);
        });
    }

    createNotificationItem(notification) {
        const div = document.createElement('div');
        div.className = `notification-item ${!notification.read ? 'unread' : ''}`;
        
        div.innerHTML = `
            <div class="notification-icon">
                <i class="bi ${notification.icon} ${notification.iconColor}"></i>
            </div>
            <div class="notification-content">
                <h6>${notification.title}</h6>
                <p>${notification.message}</p>
                <small>${notification.time}</small>
            </div>
            <button class="notification-close" onclick="cubeApp.dismissNotification('${notification.id}')">
                <i class="bi bi-x"></i>
            </button>
        `;
        
        return div;
    }

    updateNotificationBadge() {
        const badge = document.getElementById('notification-badge');
        const unreadCount = this.notifications.filter(n => !n.read).length;
        
        if (badge) {
            badge.textContent = unreadCount;
            badge.style.display = unreadCount > 0 ? 'flex' : 'none';
        }
    }

    updateUserInfo() {
        if (!this.user) return;
        
        // Update user name and email in header
        const userNameElements = document.querySelectorAll('.user-name');
        const userEmailElements = document.querySelectorAll('.user-details p');
        
        userNameElements.forEach(el => el.textContent = this.user.name);
        userEmailElements.forEach(el => el.textContent = this.user.email);
    }

    // ===== SEARCH FUNCTIONALITY =====
    handleGlobalSearch(event) {
        const query = event.target.value.trim();
        
        if (query.length > 2) {
            clearTimeout(this.searchTimeout);
            this.searchTimeout = setTimeout(() => {
                this.performSearch(query);
            }, 300);
        } else {
            this.hideSearchSuggestions();
        }
    }

    performSearch(query) {
        console.log(`🔍 Searching for: "${query}"`);
        
        // Simulate search results
        const mockResults = [
            { type: 'candidate', name: 'Rajesh Kumar Murugan', position: 'Senior AI Engineer' },
            { type: 'job', title: 'Frontend Developer', department: 'Engineering' },
            { type: 'contact', name: 'Priya Lakshmi Sundaram', company: 'Zoho Corporation' }
        ].filter(item =>
            JSON.stringify(item).toLowerCase().includes(query.toLowerCase())
        );
        
        this.showSearchSuggestions(mockResults);
    }

    showSearchSuggestions(results) {
        const suggestions = document.getElementById('search-suggestions');
        if (!suggestions) return;
        
        if (results.length === 0) {
            suggestions.innerHTML = '<div class="no-results">No results found</div>';
        } else {
            suggestions.innerHTML = results.map(result => 
                `<div class="suggestion-item" onclick="cubeApp.selectSearchResult('${result.type}', '${result.name || result.title}')">
                    <i class="bi bi-${this.getSearchIcon(result.type)}"></i>
                    <span>${result.name || result.title}</span>
                </div>`
            ).join('');
        }
        
        suggestions.style.display = 'block';
    }

    hideSearchSuggestions() {
        const suggestions = document.getElementById('search-suggestions');
        if (suggestions) {
            suggestions.style.display = 'none';
        }
    }

    getSearchIcon(type) {
        const icons = {
            candidate: 'person',
            job: 'briefcase',
            contact: 'person-lines-fill'
        };
        return icons[type] || 'search';
    }

    selectSearchResult(type, name) {
        console.log(`📋 Selected ${type}: ${name}`);
        this.hideSearchSuggestions();
        this.showInfoToast(`Opening ${type}: ${name}`);
    }

    // ===== REAL-TIME UPDATES =====
    startRealTimeUpdates() {
        // Simulate real-time notifications
        setInterval(() => {
            if (Math.random() < 0.1) { // 10% chance every 30 seconds
                this.addRandomNotification();
            }
        }, 30000);
        
        // Update timestamps
        setInterval(() => {
            this.updateTimestamps();
        }, 60000); // Every minute
    }

    addRandomNotification() {
        const randomNotifications = [
            {
                type: 'application',
                title: 'New application received',
                message: 'A candidate applied for Backend Developer position',
                icon: 'bi-person-plus',
                iconColor: 'text-success'
            },
            {
                type: 'interview',
                title: 'Interview reminder',
                message: 'You have an interview in 30 minutes',
                icon: 'bi-clock',
                iconColor: 'text-warning'
            },
            {
                type: 'status',
                title: 'Candidate status updated',
                message: 'Alex Johnson moved to technical interview round',
                icon: 'bi-arrow-up-circle',
                iconColor: 'text-info'
            }
        ];
        
        const randomNotification = randomNotifications[Math.floor(Math.random() * randomNotifications.length)];
        
        const notification = {
            id: Date.now(),
            ...randomNotification,
            time: 'Just now',
            read: false,
            timestamp: new Date()
        };
        
        this.notifications.unshift(notification);
        this.updateNotificationBadge();
        this.renderNotifications();
        
        // Show toast
        this.showInfoToast(notification.title);
    }

    updateTimestamps() {
        // Update relative timestamps in notifications and activities
        this.notifications.forEach(notification => {
            if (notification.timestamp) {
                notification.time = this.getRelativeTime(notification.timestamp);
            }
        });
        
        this.activities.forEach(activity => {
            if (activity.timestamp) {
                activity.time = this.getRelativeTime(activity.timestamp);
            }
        });
        
        this.renderNotifications();
        this.renderActivityTimeline();
    }

    startActivityTracking() {
        // Track user activity for session management
        let lastActivity = Date.now();
        
        document.addEventListener('click', () => {
            lastActivity = Date.now();
            if (this.user) {
                this.user.lastActivity = new Date().toISOString();
            }
        });
        
        // Check for inactivity
        setInterval(() => {
            const inactiveTime = Date.now() - lastActivity;
            const inactiveMinutes = inactiveTime / (1000 * 60);
            
            if (inactiveMinutes > 30) { // 30 minutes of inactivity
                this.showWarningToast('You\'ve been inactive for 30 minutes. Your session will expire soon.');
            }
        }, 60000); // Check every minute
    }

    // ===== UTILITY FUNCTIONS =====
    delay(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }

    getRelativeTime(timestamp) {
        const now = new Date();
        const time = new Date(timestamp);
        const diffInSeconds = Math.floor((now - time) / 1000);
        
        if (diffInSeconds < 60) return 'Just now';
        if (diffInSeconds < 3600) return `${Math.floor(diffInSeconds / 60)} minutes ago`;
        if (diffInSeconds < 86400) return `${Math.floor(diffInSeconds / 3600)} hours ago`;
        return `${Math.floor(diffInSeconds / 86400)} days ago`;
    }

    getDisplayName(email) {
        const names = {
            '<EMAIL>': 'Administrator',
            '<EMAIL>': 'Demo User',
            '<EMAIL>': 'Test User'
        };
        return names[email.toLowerCase()] || email.split('@')[0];
    }

    getRandomAvatar() {
        const avatars = [
            'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=40&h=40&fit=crop&crop=face',
            'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=40&h=40&fit=crop&crop=face',
            'https://images.unsplash.com/photo-1494790108755-2616b612b287?w=40&h=40&fit=crop&crop=face'
        ];
        return avatars[Math.floor(Math.random() * avatars.length)];
    }

    isValidEmail(email) {
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        return emailRegex.test(email);
    }

    saveUserSession() {
        if (this.user && localStorage.getItem('cubeRecruit_rememberMe')) {
            localStorage.setItem('cubeRecruit_user', JSON.stringify(this.user));
        }
    }

    // ===== FORM VALIDATION =====
    setupFormValidation() {
        const forms = document.querySelectorAll('form');
        
        forms.forEach(form => {
            const inputs = form.querySelectorAll('input[required]');
            
            inputs.forEach(input => {
                input.addEventListener('blur', () => this.validateField(input));
                input.addEventListener('input', () => this.clearFieldError(input.id));
            });
        });
    }

    validateField(field) {
        const value = field.value.trim();
        const fieldType = field.type;
        const fieldId = field.id;
        
        // Clear previous error
        this.clearFieldError(fieldId);
        
        // Required validation
        if (field.hasAttribute('required') && !value) {
            this.showFieldError(fieldId, 'This field is required');
            return false;
        }
        
        // Email validation
        if (fieldType === 'email' && value && !this.isValidEmail(value)) {
            this.showFieldError(fieldId, 'Please enter a valid email address');
            return false;
        }
        
        // Password validation
        if (fieldType === 'password' && value && value.length < 6) {
            this.showFieldError(fieldId, 'Password must be at least 6 characters');
            return false;
        }
        
        return true;
    }

    showFieldError(fieldId, message) {
        const field = document.getElementById(fieldId);
        const feedback = field.parentElement.querySelector('.invalid-feedback');
        
        field.classList.add('is-invalid');
        if (feedback) {
            feedback.textContent = message;
        }
    }

    clearFieldError(fieldId) {
        const field = document.getElementById(fieldId);
        field.classList.remove('is-invalid');
    }

    clearFormErrors() {
        document.querySelectorAll('.is-invalid').forEach(field => {
            field.classList.remove('is-invalid');
        });
    }

    markFieldAsInvalid(fieldId) {
        const field = document.getElementById(fieldId);
        field.classList.add('is-invalid');
    }

    // ===== LOADING SCREEN =====
    showLoadingScreen() {
        const loadingScreen = document.getElementById('loading-screen');
        if (loadingScreen) {
            loadingScreen.style.display = 'flex';
        }
    }

    hideLoadingScreen() {
        const loadingScreen = document.getElementById('loading-screen');
        if (loadingScreen) {
            loadingScreen.classList.add('fade-out');
            setTimeout(() => {
                loadingScreen.style.display = 'none';
            }, 300);
        }
    }

    // ===== TOAST NOTIFICATIONS =====
    showSuccessToast(message) {
        this.showToast(message, 'success');
    }

    showErrorToast(message) {
        this.showToast(message, 'error');
    }

    showWarningToast(message) {
        this.showToast(message, 'warning');
    }

    showInfoToast(message) {
        this.showToast(message, 'info');
    }

    showToast(message, type = 'info') {
        const toast = document.createElement('div');
        toast.className = `toast-notification toast-${type}`;
        
        const colors = {
            success: '#10b981',
            error: '#ef4444',
            warning: '#f59e0b',
            info: '#3b82f6'
        };
        
        const icons = {
            success: 'bi-check-circle',
            error: 'bi-exclamation-circle',
            warning: 'bi-exclamation-triangle',
            info: 'bi-info-circle'
        };
        
        toast.innerHTML = `
            <div class="toast-content">
                <i class="bi ${icons[type]}"></i>
                <span>${message}</span>
            </div>
            <button class="toast-close" onclick="this.parentElement.remove()">
                <i class="bi bi-x"></i>
            </button>
        `;
        
        // Apply styles
        Object.assign(toast.style, {
            position: 'fixed',
            top: '20px',
            right: '20px',
            background: colors[type],
            color: 'white',
            padding: '12px 16px',
            borderRadius: '8px',
            boxShadow: '0 4px 12px rgba(0,0,0,0.15)',
            zIndex: '10000',
            display: 'flex',
            alignItems: 'center',
            gap: '12px',
            minWidth: '300px',
            maxWidth: '500px',
            animation: 'slideInRight 0.3s ease-out',
            fontSize: '14px',
            fontWeight: '500'
        });
        
        document.body.appendChild(toast);
        
        // Auto remove
        setTimeout(() => {
            if (toast.parentElement) {
                toast.style.animation = 'slideOutRight 0.3s ease-in';
                setTimeout(() => toast.remove(), 300);
            }
        }, 5000);
    }

    // ===== EVENT HANDLERS =====
    handleActivityAction(activityId, type) {
        console.log(`🎯 Activity action: ${type} for ID: ${activityId}`);
        this.showInfoToast(`Opening ${type} details...`);
    }

    handleResize() {
        const width = window.innerWidth;
        
        if (width < 768) {
            this.adjustForMobile();
        } else {
            this.adjustForDesktop();
        }
    }

    adjustForMobile() {
        console.log('📱 Adjusting for mobile view');
        // Mobile-specific adjustments
    }

    adjustForDesktop() {
        console.log('🖥️ Adjusting for desktop view');
        // Desktop-specific adjustments
    }

    dismissNotification(notificationId) {
        this.notifications = this.notifications.filter(n => n.id != notificationId);
        this.updateNotificationBadge();
        this.renderNotifications();
    }

    markAllNotificationsRead() {
        this.notifications.forEach(notification => {
            notification.read = true;
        });
        
        this.updateNotificationBadge();
        this.renderNotifications();
        this.showSuccessToast('All notifications marked as read');
    }

    closeAllModals() {
        // Close any open Bootstrap modals
        const modals = document.querySelectorAll('.modal.show');
        modals.forEach(modal => {
            const modalInstance = bootstrap.Modal.getInstance(modal);
            if (modalInstance) {
                modalInstance.hide();
            }
        });
    }

    // ===== PLACEHOLDER FUNCTIONS =====
    showNotImplementedMessage(feature) {
        this.showInfoToast(`${feature} feature will be implemented in the next version.`);
    }
}

// ===== GLOBAL FUNCTIONS =====
function togglePassword() {
    const passwordInput = document.getElementById('password');
    const passwordIcon = document.getElementById('password-icon');
    
    if (passwordInput.type === 'password') {
        passwordInput.type = 'text';
        passwordIcon.className = 'bi bi-eye-slash';
    } else {
        passwordInput.type = 'password';
        passwordIcon.className = 'bi bi-eye';
    }
}

function logout() {
    if (window.cubeApp) {
        window.cubeApp.logout();
    }
}

function switchModule(module) {
    if (window.cubeApp) {
        window.cubeApp.switchModule(module);
    }
}

function markAllNotificationsRead() {
    if (window.cubeApp) {
        window.cubeApp.markAllNotificationsRead();
    }
}

// Modal functions
function showAddCandidateModal() {
    cubeApp.showNotImplementedMessage('Add Candidate Modal');
}

function showAddJobModal() {
    cubeApp.showNotImplementedMessage('Add Job Modal');
}

function showScheduleModal() {
    cubeApp.showNotImplementedMessage('Schedule Interview Modal');
}

function showQuickAddMenu() {
    cubeApp.showNotImplementedMessage('Quick Add Menu');
}

function showImportModal() {
    cubeApp.showNotImplementedMessage('Import Data Modal');
}

function showExportModal() {
    cubeApp.showNotImplementedMessage('Export Data Modal');
}

function customizeDashboard() {
    cubeApp.showNotImplementedMessage('Dashboard Customization');
}

function showBulkImport() {
    cubeApp.showNotImplementedMessage('Bulk Import');
}

function showContactAdmin() {
    cubeApp.showInfoToast('Please contact your administrator for account access.');
}

function showForgotPassword() {
    cubeApp.showInfoToast('Password reset functionality coming soon.');
}

// ===== INITIALIZATION =====
document.addEventListener('DOMContentLoaded', function() {
    console.log('🎉 DOM Content Loaded - Initializing Cube AI Recruit Portal');
    
    // Initialize the application
    window.cubeApp = new CubeRecruitApp();
    
    // Check URL hash for initial module
    setTimeout(() => {
        const hash = window.location.hash.replace('#', '');
        if (hash && hash !== 'dashboard') {
            window.cubeApp.switchModule(hash);
        }
    }, 100);
    
    console.log('✨ Cube AI Recruit Portal initialized successfully!');
});

// ===== TOAST ANIMATIONS =====
const toastStyles = document.createElement('style');
toastStyles.textContent = `
    @keyframes slideInRight {
        from {
            transform: translateX(100%);
            opacity: 0;
        }
        to {
            transform: translateX(0);
            opacity: 1;
        }
    }
    
    @keyframes slideOutRight {
        from {
            transform: translateX(0);
            opacity: 1;
        }
        to {
            transform: translateX(100%);
            opacity: 0;
        }
    }
    
    .toast-content {
        display: flex;
        align-items: center;
        gap: 8px;
        flex: 1;
    }
    
    .toast-close {
        background: none;
        border: none;
        color: inherit;
        cursor: pointer;
        padding: 4px;
        border-radius: 4px;
        opacity: 0.8;
        transition: opacity 0.2s;
    }
    
    .toast-close:hover {
        opacity: 1;
        background: rgba(255,255,255,0.1);
    }
    
    .search-suggestions {
        position: absolute;
        top: 100%;
        left: 0;
        right: 0;
        background: white;
        border: 1px solid #e5e7eb;
        border-radius: 8px;
        box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
        z-index: 1000;
        max-height: 300px;
        overflow-y: auto;
        display: none;
    }
    
    .suggestion-item {
        display: flex;
        align-items: center;
        gap: 12px;
        padding: 12px 16px;
        cursor: pointer;
        transition: background-color 0.15s;
        border-bottom: 1px solid #f3f4f6;
    }
    
    .suggestion-item:last-child {
        border-bottom: none;
    }
    
    .suggestion-item:hover {
        background-color: #f9fafb;
    }
    
    .suggestion-item i {
        color: #6b7280;
        width: 16px;
    }
    
    .no-results {
        padding: 16px;
        text-align: center;
        color: #6b7280;
        font-style: italic;
    }
    
    .notification-item {
        display: flex;
        align-items: flex-start;
        gap: 12px;
        padding: 16px;
        border-bottom: 1px solid #f3f4f6;
        transition: background-color 0.15s;
    }
    
    .notification-item:hover {
        background-color: #f9fafb;
    }
    
    .notification-item.unread {
        background-color: rgba(59, 130, 246, 0.02);
        border-left: 3px solid #3b82f6;
    }
    
    .notification-icon {
        font-size: 18px;
        margin-top: 2px;
    }
    
    .notification-content {
        flex: 1;
    }
    
    .notification-content h6 {
        margin: 0 0 4px 0;
        font-size: 14px;
        font-weight: 600;
        color: #374151;
    }
    
    .notification-content p {
        margin: 0 0 4px 0;
        font-size: 13px;
        color: #6b7280;
        line-height: 1.4;
    }
    
    .notification-content small {
        font-size: 12px;
        color: #9ca3af;
    }
    
    .notification-close {
        background: none;
        border: none;
        color: #9ca3af;
        cursor: pointer;
        padding: 4px;
        border-radius: 4px;
        transition: all 0.15s;
    }
    
    .notification-close:hover {
        background-color: #f3f4f6;
        color: #6b7280;
    }
`;
document.head.appendChild(toastStyles);

// Performance monitoring
if (typeof performance !== 'undefined') {
    window.addEventListener('load', () => {
        setTimeout(() => {
            const loadTime = performance.timing.loadEventEnd - performance.timing.navigationStart;
            console.log(`⚡ Page loaded in ${loadTime}ms`);
        }, 0);
    });
}
<script src="scripts.js"></script>
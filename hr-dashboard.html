<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>CubeAI HR Dashboard - Resume Screening & ATS</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.1/font/bootstrap-icons.css" rel="stylesheet">
    <link href="analyzer-enhanced.css" rel="stylesheet">
    <style>
        .hr-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 2rem 0;
        }
        .feature-card {
            transition: transform 0.3s ease;
            border: none;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
        }
        .feature-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 15px rgba(0,0,0,0.2);
        }
        .ats-score {
            font-size: 2rem;
            font-weight: bold;
        }
        .score-excellent { color: #28a745; }
        .score-good { color: #17a2b8; }
        .score-average { color: #ffc107; }
        .score-poor { color: #dc3545; }
        .candidate-card {
            border-left: 4px solid #667eea;
            margin-bottom: 1rem;
        }
        .job-requirements {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 1rem;
        }
        .skill-match {
            display: inline-block;
            margin: 2px;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 0.8rem;
        }
        .skill-matched { background: #d4edda; color: #155724; }
        .skill-missing { background: #f8d7da; color: #721c24; }
        .bulk-upload-area {
            border: 2px dashed #667eea;
            border-radius: 8px;
            padding: 2rem;
            text-align: center;
            background: #f8f9fa;
            transition: all 0.3s ease;
        }
        .bulk-upload-area:hover {
            background: #e9ecef;
            border-color: #5a6fd8;
        }
        .progress-container {
            margin-top: 1rem;
        }
    </style>
</head>
<body>
    <!-- HR Header -->
    <header class="hr-header">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-md-8">
                    <h1 class="display-5 mb-2">
                        <i class="bi bi-people-fill me-3"></i>
                        CubeAI HR Dashboard
                    </h1>
                    <p class="lead mb-0">AI-Powered Resume Screening & ATS Scoring System</p>
                </div>
                <div class="col-md-4 text-end">
                    <div class="d-flex justify-content-end align-items-center">
                        <div class="me-3">
                            <small>Database Status:</small><br>
                            <span id="dbStatus" class="badge bg-success">Connected</span>
                        </div>
                        <div>
                            <small>Total CVs:</small><br>
                            <span id="totalCVs" class="badge bg-info">0</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </header>

    <main class="container my-5">
        <!-- Navigation Tabs -->
        <ul class="nav nav-pills nav-fill mb-4" id="hrTabs" role="tablist">
            <li class="nav-item" role="presentation">
                <button class="nav-link active" id="cv-management-tab" data-bs-toggle="pill" data-bs-target="#cv-management" type="button" role="tab">
                    <i class="bi bi-file-earmark-person me-2"></i>CV Management
                </button>
            </li>
            <li class="nav-item" role="presentation">
                <button class="nav-link" id="job-creation-tab" data-bs-toggle="pill" data-bs-target="#job-creation" type="button" role="tab">
                    <i class="bi bi-briefcase me-2"></i>Job Creation
                </button>
            </li>
            <li class="nav-item" role="presentation">
                <button class="nav-link" id="ats-analysis-tab" data-bs-toggle="pill" data-bs-target="#ats-analysis" type="button" role="tab">
                    <i class="bi bi-graph-up me-2"></i>ATS Analysis
                </button>
            </li>
            <li class="nav-item" role="presentation">
                <button class="nav-link" id="hr-reports-tab" data-bs-toggle="pill" data-bs-target="#hr-reports" type="button" role="tab">
                    <i class="bi bi-file-earmark-bar-graph me-2"></i>HR Reports
                </button>
            </li>
        </ul>

        <!-- Tab Content -->
        <div class="tab-content" id="hrTabContent">
            <!-- CV Management Tab -->
            <div class="tab-pane fade show active" id="cv-management" role="tabpanel">
                <div class="row">
                    <!-- Bulk CV Upload -->
                    <div class="col-md-6">
                        <div class="card feature-card">
                            <div class="card-header bg-primary text-white">
                                <h5 class="mb-0">
                                    <i class="bi bi-cloud-upload me-2"></i>
                                    Bulk CV Upload
                                </h5>
                            </div>
                            <div class="card-body">
                                <div class="bulk-upload-area" id="bulkUploadArea">
                                    <i class="bi bi-cloud-upload display-4 text-muted mb-3"></i>
                                    <h6>Drag & Drop Multiple CVs Here</h6>
                                    <p class="text-muted">Or click to select files</p>
                                    <input type="file" id="bulkFileInput" multiple accept=".pdf,.doc,.docx,.txt" style="display: none;">
                                    <button class="btn btn-outline-primary" onclick="document.getElementById('bulkFileInput').click()">
                                        Select Files
                                    </button>
                                </div>
                                <div id="uploadProgress" class="progress-container" style="display: none;">
                                    <div class="progress">
                                        <div id="uploadProgressBar" class="progress-bar progress-bar-striped progress-bar-animated" role="progressbar"></div>
                                    </div>
                                    <div id="uploadStatus" class="mt-2 text-center"></div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Manual CV Entry -->
                    <div class="col-md-6">
                        <div class="card feature-card">
                            <div class="card-header bg-success text-white">
                                <h5 class="mb-0">
                                    <i class="bi bi-person-plus me-2"></i>
                                    Manual CV Entry
                                </h5>
                            </div>
                            <div class="card-body">
                                <form id="manualCVForm">
                                    <div class="mb-3">
                                        <label class="form-label">Candidate Name</label>
                                        <input type="text" class="form-control" id="candidateName" required>
                                    </div>
                                    <div class="mb-3">
                                        <label class="form-label">Email</label>
                                        <input type="email" class="form-control" id="candidateEmail" required>
                                    </div>
                                    <div class="mb-3">
                                        <label class="form-label">Phone</label>
                                        <input type="tel" class="form-control" id="candidatePhone" required>
                                    </div>
                                    <div class="mb-3">
                                        <label class="form-label">Position</label>
                                        <input type="text" class="form-control" id="candidatePosition" required>
                                    </div>
                                    <div class="mb-3">
                                        <label class="form-label">Experience (Years)</label>
                                        <input type="number" class="form-control" id="candidateExperience" min="0" max="50" required>
                                    </div>
                                    <div class="mb-3">
                                        <label class="form-label">Skills (comma-separated)</label>
                                        <textarea class="form-control" id="candidateSkills" rows="2" placeholder="JavaScript, React, Node.js, Python"></textarea>
                                    </div>
                                    <div class="mb-3">
                                        <label class="form-label">Resume Text</label>
                                        <textarea class="form-control" id="candidateResume" rows="6" required></textarea>
                                    </div>
                                    <button type="submit" class="btn btn-success w-100">
                                        <i class="bi bi-plus-circle me-2"></i>Add to Database
                                    </button>
                                </form>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- CV Database View -->
                <div class="row mt-4">
                    <div class="col-12">
                        <div class="card feature-card">
                            <div class="card-header bg-info text-white d-flex justify-content-between align-items-center">
                                <h5 class="mb-0">
                                    <i class="bi bi-database me-2"></i>
                                    CV Database
                                </h5>
                                <div>
                                    <button class="btn btn-light btn-sm me-2" id="refreshCVs">
                                        <i class="bi bi-arrow-clockwise"></i> Refresh
                                    </button>
                                    <button class="btn btn-warning btn-sm" id="exportCVs">
                                        <i class="bi bi-download"></i> Export
                                    </button>
                                </div>
                            </div>
                            <div class="card-body">
                                <div class="row mb-3">
                                    <div class="col-md-6">
                                        <input type="text" class="form-control" id="cvSearchInput" placeholder="Search candidates...">
                                    </div>
                                    <div class="col-md-3">
                                        <select class="form-select" id="experienceFilter">
                                            <option value="">All Experience Levels</option>
                                            <option value="0-2">0-2 years</option>
                                            <option value="3-5">3-5 years</option>
                                            <option value="6-10">6-10 years</option>
                                            <option value="10+">10+ years</option>
                                        </select>
                                    </div>
                                    <div class="col-md-3">
                                        <select class="form-select" id="skillsFilter">
                                            <option value="">All Skills</option>
                                            <option value="JavaScript">JavaScript</option>
                                            <option value="Python">Python</option>
                                            <option value="React">React</option>
                                            <option value="Node.js">Node.js</option>
                                            <option value="Java">Java</option>
                                        </select>
                                    </div>
                                </div>
                                <div id="cvDatabaseList">
                                    <!-- CV list will be populated here -->
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Job Creation Tab -->
            <div class="tab-pane fade" id="job-creation" role="tabpanel">
                <div class="row">
                    <div class="col-md-8">
                        <div class="card feature-card">
                            <div class="card-header bg-warning text-dark">
                                <h5 class="mb-0">
                                    <i class="bi bi-plus-square me-2"></i>
                                    Create New Job Role
                                </h5>
                            </div>
                            <div class="card-body">
                                <form id="jobCreationForm">
                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label class="form-label">Job Title</label>
                                                <input type="text" class="form-control" id="jobTitle" required>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label class="form-label">Department</label>
                                                <select class="form-select" id="jobDepartment" required>
                                                    <option value="">Select Department</option>
                                                    <option value="Engineering">Engineering</option>
                                                    <option value="Data Science">Data Science</option>
                                                    <option value="Product">Product</option>
                                                    <option value="Design">Design</option>
                                                    <option value="Marketing">Marketing</option>
                                                    <option value="Sales">Sales</option>
                                                    <option value="HR">Human Resources</option>
                                                </select>
                                            </div>
                                        </div>
                                    </div>
                                    
                                    <div class="row">
                                        <div class="col-md-4">
                                            <div class="mb-3">
                                                <label class="form-label">Experience Required</label>
                                                <select class="form-select" id="jobExperience" required>
                                                    <option value="">Select Experience</option>
                                                    <option value="0-1">0-1 years (Fresher)</option>
                                                    <option value="1-3">1-3 years (Junior)</option>
                                                    <option value="3-5">3-5 years (Mid-level)</option>
                                                    <option value="5-8">5-8 years (Senior)</option>
                                                    <option value="8+">8+ years (Lead/Principal)</option>
                                                </select>
                                            </div>
                                        </div>
                                        <div class="col-md-4">
                                            <div class="mb-3">
                                                <label class="form-label">Job Type</label>
                                                <select class="form-select" id="jobType" required>
                                                    <option value="">Select Type</option>
                                                    <option value="Full-time">Full-time</option>
                                                    <option value="Part-time">Part-time</option>
                                                    <option value="Contract">Contract</option>
                                                    <option value="Internship">Internship</option>
                                                </select>
                                            </div>
                                        </div>
                                        <div class="col-md-4">
                                            <div class="mb-3">
                                                <label class="form-label">Location</label>
                                                <input type="text" class="form-control" id="jobLocation" placeholder="Chennai, Tamil Nadu" required>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="mb-3">
                                        <label class="form-label">Required Skills</label>
                                        <textarea class="form-control" id="jobSkills" rows="2" placeholder="JavaScript, React, Node.js, MongoDB, AWS" required></textarea>
                                        <div class="form-text">Enter skills separated by commas</div>
                                    </div>

                                    <div class="mb-3">
                                        <label class="form-label">Job Description</label>
                                        <textarea class="form-control" id="jobDescription" rows="8" required placeholder="Enter detailed job description including responsibilities, requirements, and qualifications..."></textarea>
                                    </div>

                                    <div class="mb-3">
                                        <label class="form-label">ATS Keywords (for better matching)</label>
                                        <textarea class="form-control" id="jobATSKeywords" rows="2" placeholder="software engineer, full stack, web development, agile, scrum"></textarea>
                                        <div class="form-text">Keywords that ATS should prioritize when scoring candidates</div>
                                    </div>

                                    <button type="submit" class="btn btn-warning w-100">
                                        <i class="bi bi-plus-circle me-2"></i>Create Job Role
                                    </button>
                                </form>
                            </div>
                        </div>
                    </div>

                    <div class="col-md-4">
                        <div class="card feature-card">
                            <div class="card-header bg-secondary text-white">
                                <h5 class="mb-0">
                                    <i class="bi bi-list-ul me-2"></i>
                                    Existing Jobs
                                </h5>
                            </div>
                            <div class="card-body">
                                <div id="existingJobsList">
                                    <!-- Existing jobs will be populated here -->
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- ATS Analysis Tab -->
            <div class="tab-pane fade" id="ats-analysis" role="tabpanel">
                <div class="row">
                    <div class="col-12">
                        <div class="card feature-card">
                            <div class="card-header bg-danger text-white">
                                <h5 class="mb-0">
                                    <i class="bi bi-cpu me-2"></i>
                                    ATS Analysis & Scoring
                                </h5>
                            </div>
                            <div class="card-body">
                                <div class="row mb-4">
                                    <div class="col-md-6">
                                        <label class="form-label">Select Job Role</label>
                                        <select class="form-select" id="analysisJobSelect">
                                            <option value="">Choose a job role for analysis...</option>
                                        </select>
                                    </div>
                                    <div class="col-md-6">
                                        <label class="form-label">Analysis Options</label>
                                        <div class="d-flex gap-2">
                                            <button class="btn btn-danger" id="analyzeAllBtn">
                                                <i class="bi bi-lightning me-2"></i>Analyze All CVs
                                            </button>
                                            <button class="btn btn-outline-danger" id="analyzeSelectedBtn" disabled>
                                                <i class="bi bi-check-square me-2"></i>Analyze Selected
                                            </button>
                                        </div>
                                    </div>
                                </div>

                                <div id="analysisProgress" class="mb-4" style="display: none;">
                                    <div class="progress">
                                        <div id="analysisProgressBar" class="progress-bar progress-bar-striped progress-bar-animated bg-danger" role="progressbar"></div>
                                    </div>
                                    <div id="analysisStatus" class="mt-2 text-center"></div>
                                </div>

                                <div id="atsResults">
                                    <!-- ATS results will be populated here -->
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- HR Reports Tab -->
            <div class="tab-pane fade" id="hr-reports" role="tabpanel">
                <div class="row">
                    <div class="col-12">
                        <div class="card feature-card">
                            <div class="card-header bg-dark text-white">
                                <h5 class="mb-0">
                                    <i class="bi bi-graph-up me-2"></i>
                                    HR Analytics & Reports
                                </h5>
                            </div>
                            <div class="card-body">
                                <div class="row mb-4">
                                    <div class="col-md-3">
                                        <div class="card text-center border-primary">
                                            <div class="card-body">
                                                <h3 class="text-primary" id="totalCandidatesCount">0</h3>
                                                <p class="mb-0">Total Candidates</p>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="card text-center border-success">
                                            <div class="card-body">
                                                <h3 class="text-success" id="qualifiedCandidatesCount">0</h3>
                                                <p class="mb-0">Qualified (>80%)</p>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="card text-center border-warning">
                                            <div class="card-body">
                                                <h3 class="text-warning" id="averageATSScore">0</h3>
                                                <p class="mb-0">Avg ATS Score</p>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="card text-center border-info">
                                            <div class="card-body">
                                                <h3 class="text-info" id="activeJobsCount">0</h3>
                                                <p class="mb-0">Active Jobs</p>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <div class="row">
                                    <div class="col-md-6">
                                        <button class="btn btn-primary w-100 mb-3" id="generateDetailedReport">
                                            <i class="bi bi-file-earmark-pdf me-2"></i>Generate Detailed Report
                                        </button>
                                    </div>
                                    <div class="col-md-6">
                                        <button class="btn btn-success w-100 mb-3" id="exportCandidateData">
                                            <i class="bi bi-file-earmark-excel me-2"></i>Export Candidate Data
                                        </button>
                                    </div>
                                </div>

                                <div id="hrReportsContent">
                                    <!-- Reports content will be populated here -->
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
    <script src="demo-data.js"></script>
    <script src="database-service.js"></script>
    <script src="hr-dashboard.js"></script>
</body>
</html>

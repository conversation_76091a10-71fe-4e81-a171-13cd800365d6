<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>CubeAI HR AI Automation Agent - Intelligent Recruitment System</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.1/font/bootstrap-icons.css" rel="stylesheet">
    <link href="analyzer-enhanced.css" rel="stylesheet">
    <style>
        .hr-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 2rem 0;
        }
        .feature-card {
            transition: transform 0.3s ease;
            border: none;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
        }
        .feature-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 15px rgba(0,0,0,0.2);
        }
        .ai-score {
            font-size: 2rem;
            font-weight: bold;
        }
        .score-excellent { color: #28a745; }
        .score-good { color: #17a2b8; }
        .score-average { color: #ffc107; }
        .score-poor { color: #dc3545; }
        .candidate-card {
            border-left: 4px solid #667eea;
            margin-bottom: 1rem;
        }
        .job-requirements {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 1rem;
        }
        .skill-match {
            display: inline-block;
            margin: 2px;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 0.8rem;
        }
        .skill-matched { background: #d4edda; color: #155724; }
        .skill-missing { background: #f8d7da; color: #721c24; }

        /* Chat Interface Styles */
        .chat-message {
            animation: fadeInUp 0.3s ease;
        }

        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(10px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .chat-message .bg-white {
            border: 1px solid #e9ecef;
        }

        .chat-message .bg-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
        }

        /* Job Description Styles */
        .job-description-content {
            animation: fadeIn 0.3s ease;
        }

        @keyframes fadeIn {
            from { opacity: 0; }
            to { opacity: 1; }
        }

        /* Google Drive Integration Styles */
        .integration-widget .status-indicator {
            display: flex;
            align-items: center;
        }

        .stat-mini {
            text-align: center;
            padding: 0.5rem;
        }

        .stat-mini h6 {
            font-size: 1.2rem;
            font-weight: bold;
            color: #495057;
        }
        .bulk-upload-area {
            border: 2px dashed #667eea;
            border-radius: 8px;
            padding: 2rem;
            text-align: center;
            background: #f8f9fa;
            transition: all 0.3s ease;
        }
        .bulk-upload-area:hover {
            background: #e9ecef;
            border-color: #5a6fd8;
        }
        .progress-container {
            margin-top: 1rem;
        }
    </style>
</head>
<body>
    <!-- HR Header -->
    <header class="hr-header">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-md-8">
                    <h1 class="display-5 mb-2">
                        <i class="bi bi-robot me-3"></i>
                        CubeAI HR AI Automation Agent
                    </h1>
                    <p class="lead mb-0">Intelligent Recruitment Automation System</p>
                </div>
                <div class="col-md-4 text-end">
                    <div class="d-flex justify-content-end align-items-center">
                        <div class="me-3">
                            <small>Database Status:</small><br>
                            <span id="dbStatus" class="badge bg-success">Connected</span>
                        </div>
                        <div>
                            <small>Total CVs:</small><br>
                            <span id="totalCVs" class="badge bg-info">0</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </header>

    <main class="container my-5">
        <!-- Navigation Tabs -->
        <ul class="nav nav-pills nav-fill mb-4" id="hrTabs" role="tablist">
            <li class="nav-item" role="presentation">
                <button class="nav-link active" id="database-connection-tab" data-bs-toggle="pill" data-bs-target="#database-connection" type="button" role="tab">
                    <i class="bi bi-database me-2"></i>Database Connection
                </button>
            </li>
            <li class="nav-item" role="presentation">
                <button class="nav-link" id="cv-management-tab" data-bs-toggle="pill" data-bs-target="#cv-management" type="button" role="tab">
                    <i class="bi bi-file-earmark-person me-2"></i>CV Management
                </button>
            </li>
            <li class="nav-item" role="presentation">
                <button class="nav-link" id="job-creation-tab" data-bs-toggle="pill" data-bs-target="#job-creation" type="button" role="tab">
                    <i class="bi bi-briefcase me-2"></i>Job Creation
                </button>
            </li>
            <li class="nav-item" role="presentation">
                <button class="nav-link" id="ai-analysis-tab" data-bs-toggle="pill" data-bs-target="#ai-analysis" type="button" role="tab">
                    <i class="bi bi-cpu me-2"></i>AI Analysis
                </button>
            </li>
            <li class="nav-item" role="presentation">
                <button class="nav-link" id="ai-reports-tab" data-bs-toggle="pill" data-bs-target="#ai-reports" type="button" role="tab">
                    <i class="bi bi-robot me-2"></i>AI Reports
                </button>
            </li>
        </ul>

        <!-- Tab Content -->
        <div class="tab-content" id="hrTabContent">

            <!-- Database Connection Tab -->
            <div class="tab-pane fade show active" id="database-connection" role="tabpanel">
                <div class="row">
                    <div class="col-12">
                        <div class="card">
                            <div class="card-header bg-primary text-white">
                                <h5 class="mb-0">
                                    <i class="bi bi-database me-2"></i>
                                    External Database Connection
                                </h5>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <!-- Database Type Selection -->
                                    <div class="col-md-6">
                                        <div class="card h-100">
                                            <div class="card-header">
                                                <h6><i class="bi bi-gear me-2"></i>Database Configuration</h6>
                                            </div>
                                            <div class="card-body">
                                                <form id="databaseConfigForm">
                                                    <div class="mb-3">
                                                        <label class="form-label">Database Type</label>
                                                        <select class="form-select" id="dbType" required>
                                                            <option value="">Select Database Type</option>
                                                            <option value="mysql">MySQL</option>
                                                            <option value="postgresql">PostgreSQL</option>
                                                            <option value="mongodb">MongoDB</option>
                                                            <option value="oracle">Oracle Database</option>
                                                            <option value="sqlserver">SQL Server</option>
                                                            <option value="sqlite">SQLite</option>
                                                            <option value="demo">Demo Database (Testing)</option>
                                                        </select>
                                                    </div>

                                                    <div class="mb-3">
                                                        <label class="form-label">Host/Server</label>
                                                        <input type="text" class="form-control" id="dbHost" placeholder="localhost or server IP">
                                                    </div>

                                                    <div class="row">
                                                        <div class="col-md-6">
                                                            <div class="mb-3">
                                                                <label class="form-label">Port</label>
                                                                <input type="number" class="form-control" id="dbPort" placeholder="3306">
                                                            </div>
                                                        </div>
                                                        <div class="col-md-6">
                                                            <div class="mb-3">
                                                                <label class="form-label">Database Name</label>
                                                                <input type="text" class="form-control" id="dbName" placeholder="hr_database">
                                                            </div>
                                                        </div>
                                                    </div>

                                                    <div class="row">
                                                        <div class="col-md-6">
                                                            <div class="mb-3">
                                                                <label class="form-label">Username</label>
                                                                <input type="text" class="form-control" id="dbUsername" placeholder="database_user">
                                                            </div>
                                                        </div>
                                                        <div class="col-md-6">
                                                            <div class="mb-3">
                                                                <label class="form-label">Password</label>
                                                                <input type="password" class="form-control" id="dbPassword" placeholder="password">
                                                            </div>
                                                        </div>
                                                    </div>

                                                    <div class="d-grid gap-2">
                                                        <button type="button" class="btn btn-primary" onclick="hrDashboard.testDatabaseConnection()">
                                                            <i class="bi bi-wifi me-2"></i>Test Connection
                                                        </button>
                                                        <button type="button" class="btn btn-success" onclick="hrDashboard.connectToDatabase()">
                                                            <i class="bi bi-database-check me-2"></i>Connect to Database
                                                        </button>
                                                    </div>
                                                </form>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- Connection Status & AI Agent Info -->
                                    <div class="col-md-6">
                                        <div class="card h-100">
                                            <div class="card-header">
                                                <h6><i class="bi bi-robot me-2"></i>AI Agent Status</h6>
                                            </div>
                                            <div class="card-body">
                                                <div class="mb-4">
                                                    <h6>Connection Status</h6>
                                                    <div id="connectionStatus" class="alert alert-info">
                                                        <i class="bi bi-info-circle me-2"></i>
                                                        Not connected to external database
                                                    </div>
                                                </div>

                                                <div class="mb-4">
                                                    <h6>AI Agent Capabilities</h6>
                                                    <ul class="list-unstyled">
                                                        <li><i class="bi bi-check-circle text-success me-2"></i>Automated CV Parsing</li>
                                                        <li><i class="bi bi-check-circle text-success me-2"></i>Intelligent Job Matching</li>
                                                        <li><i class="bi bi-check-circle text-success me-2"></i>AI-Powered Scoring</li>
                                                        <li><i class="bi bi-check-circle text-success me-2"></i>Smart Recommendations</li>
                                                        <li><i class="bi bi-check-circle text-success me-2"></i>Automated Reporting</li>
                                                        <li><i class="bi bi-check-circle text-success me-2"></i>Real-time Analytics</li>
                                                    </ul>
                                                </div>

                                                <div class="mb-3">
                                                    <h6>Database Tables</h6>
                                                    <div id="databaseTables" class="small text-muted">
                                                        Connect to database to view available tables
                                                    </div>
                                                </div>

                                                <div class="mb-3">
                                                    <h6>Data Sync Options</h6>
                                                    <div class="form-check">
                                                        <input class="form-check-input" type="checkbox" id="autoSync" checked>
                                                        <label class="form-check-label" for="autoSync">
                                                            Auto-sync with external database
                                                        </label>
                                                    </div>
                                                    <div class="form-check">
                                                        <input class="form-check-input" type="checkbox" id="realTimeAnalysis" checked>
                                                        <label class="form-check-label" for="realTimeAnalysis">
                                                            Real-time AI analysis
                                                        </label>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- Quick Actions -->
                                <div class="row mt-4">
                                    <div class="col-12">
                                        <div class="card">
                                            <div class="card-header">
                                                <h6><i class="bi bi-lightning me-2"></i>Quick Actions</h6>
                                            </div>
                                            <div class="card-body">
                                                <div class="row">
                                                    <div class="col-md-3">
                                                        <button class="btn btn-outline-primary w-100 mb-2" onclick="hrDashboard.syncCandidates()">
                                                            <i class="bi bi-arrow-repeat me-2"></i>Sync Candidates
                                                        </button>
                                                    </div>
                                                    <div class="col-md-3">
                                                        <button class="btn btn-outline-success w-100 mb-2" onclick="hrDashboard.syncJobs()">
                                                            <i class="bi bi-briefcase me-2"></i>Sync Jobs
                                                        </button>
                                                    </div>
                                                    <div class="col-md-3">
                                                        <button class="btn btn-outline-info w-100 mb-2" onclick="hrDashboard.runAIAnalysis()">
                                                            <i class="bi bi-cpu me-2"></i>Run AI Analysis
                                                        </button>
                                                    </div>
                                                    <div class="col-md-3">
                                                        <button class="btn btn-outline-warning w-100 mb-2" onclick="hrDashboard.generateAIReport()">
                                                            <i class="bi bi-file-earmark-text me-2"></i>Generate Report
                                                        </button>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <!-- CV Management Tab -->
            <div class="tab-pane fade" id="cv-management" role="tabpanel">
                <div class="row">
                    <!-- Bulk CV Upload -->
                    <div class="col-md-6">
                        <div class="card feature-card">
                            <div class="card-header bg-primary text-white">
                                <h5 class="mb-0">
                                    <i class="bi bi-cloud-upload me-2"></i>
                                    Bulk CV Upload
                                </h5>
                            </div>
                            <div class="card-body">
                                <div class="bulk-upload-area" id="bulkUploadArea">
                                    <i class="bi bi-cloud-upload display-4 text-muted mb-3"></i>
                                    <h6>Drag & Drop Multiple CVs Here</h6>
                                    <p class="text-muted">Or click to select files</p>
                                    <input type="file" id="bulkFileInput" multiple accept=".pdf,.doc,.docx,.txt" style="display: none;">
                                    <button class="btn btn-outline-primary" onclick="document.getElementById('bulkFileInput').click()">
                                        Select Files
                                    </button>
                                </div>
                                <div id="uploadProgress" class="progress-container" style="display: none;">
                                    <div class="progress">
                                        <div id="uploadProgressBar" class="progress-bar progress-bar-striped progress-bar-animated" role="progressbar"></div>
                                    </div>
                                    <div id="uploadStatus" class="mt-2 text-center"></div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- AI Database Sync -->
                    <div class="col-md-6">
                        <div class="card feature-card">
                            <div class="card-header bg-success text-white">
                                <h5 class="mb-0">
                                    <i class="bi bi-robot me-2"></i>
                                    AI Database Sync
                                </h5>
                            </div>
                            <div class="card-body">
                                <div class="mb-4">
                                    <h6><i class="bi bi-database-check me-2"></i>External Database Status</h6>
                                    <div id="externalDbStatus" class="alert alert-warning">
                                        <i class="bi bi-exclamation-triangle me-2"></i>
                                        Connect to external database first
                                    </div>
                                </div>

                                <div class="mb-4">
                                    <h6><i class="bi bi-arrow-repeat me-2"></i>Sync Options</h6>
                                    <div class="form-check mb-2">
                                        <input class="form-check-input" type="checkbox" id="syncCandidates" checked>
                                        <label class="form-check-label" for="syncCandidates">
                                            Sync Candidate Data
                                        </label>
                                    </div>
                                    <div class="form-check mb-2">
                                        <input class="form-check-input" type="checkbox" id="syncJobs" checked>
                                        <label class="form-check-label" for="syncJobs">
                                            Sync Job Postings
                                        </label>
                                    </div>
                                    <div class="form-check mb-3">
                                        <input class="form-check-input" type="checkbox" id="autoAIAnalysis" checked>
                                        <label class="form-check-label" for="autoAIAnalysis">
                                            Auto AI Analysis on Sync
                                        </label>
                                    </div>
                                </div>

                                <div class="d-grid gap-2">
                                    <button class="btn btn-primary" onclick="hrDashboard.syncFromExternalDB()">
                                        <i class="bi bi-download me-2"></i>Sync from External DB
                                    </button>
                                    <button class="btn btn-success" onclick="hrDashboard.pushToExternalDB()">
                                        <i class="bi bi-upload me-2"></i>Push to External DB
                                    </button>
                                    <button class="btn btn-info" onclick="hrDashboard.runFullAIAnalysis()">
                                        <i class="bi bi-cpu me-2"></i>Run Full AI Analysis
                                    </button>
                                </div>

                                <div class="mt-3">
                                    <small class="text-muted">
                                        <i class="bi bi-info-circle me-1"></i>
                                        AI Agent will automatically process and analyze all synced data
                                    </small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- CV Database View -->
                <div class="row mt-4">
                    <div class="col-12">
                        <div class="card feature-card">
                            <div class="card-header bg-info text-white d-flex justify-content-between align-items-center">
                                <h5 class="mb-0">
                                    <i class="bi bi-database me-2"></i>
                                    CV Database
                                </h5>
                                <div>
                                    <button class="btn btn-light btn-sm me-2" id="refreshCVs">
                                        <i class="bi bi-arrow-clockwise"></i> Refresh
                                    </button>
                                    <button class="btn btn-warning btn-sm" id="exportCVs">
                                        <i class="bi bi-download"></i> Export
                                    </button>
                                </div>
                            </div>
                            <div class="card-body">
                                <div class="row mb-3">
                                    <div class="col-md-6">
                                        <input type="text" class="form-control" id="cvSearchInput" placeholder="Search candidates...">
                                    </div>
                                    <div class="col-md-3">
                                        <select class="form-select" id="experienceFilter">
                                            <option value="">All Experience Levels</option>
                                            <option value="0-2">0-2 years</option>
                                            <option value="3-5">3-5 years</option>
                                            <option value="6-10">6-10 years</option>
                                            <option value="10+">10+ years</option>
                                        </select>
                                    </div>
                                    <div class="col-md-3">
                                        <select class="form-select" id="skillsFilter">
                                            <option value="">All Skills</option>
                                            <option value="JavaScript">JavaScript</option>
                                            <option value="Python">Python</option>
                                            <option value="React">React</option>
                                            <option value="Node.js">Node.js</option>
                                            <option value="Java">Java</option>
                                        </select>
                                    </div>
                                </div>
                                <div id="cvDatabaseList">
                                    <!-- CV list will be populated here -->
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Job Creation Tab -->
            <div class="tab-pane fade" id="job-creation" role="tabpanel">
                <div class="row">
                    <div class="col-md-8">
                        <div class="card feature-card">
                            <div class="card-header bg-warning text-dark">
                                <h5 class="mb-0">
                                    <i class="bi bi-plus-square me-2"></i>
                                    Create New Job Role
                                </h5>
                            </div>
                            <div class="card-body">
                                <form id="jobCreationForm">
                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label class="form-label">Job Title</label>
                                                <input type="text" class="form-control" id="jobTitle" required>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label class="form-label">Department</label>
                                                <select class="form-select" id="jobDepartment" required>
                                                    <option value="">Select Department</option>
                                                    <option value="Engineering">Engineering</option>
                                                    <option value="Data Science">Data Science</option>
                                                    <option value="Product">Product</option>
                                                    <option value="Design">Design</option>
                                                    <option value="Marketing">Marketing</option>
                                                    <option value="Sales">Sales</option>
                                                    <option value="HR">Human Resources</option>
                                                </select>
                                            </div>
                                        </div>
                                    </div>
                                    
                                    <div class="row">
                                        <div class="col-md-4">
                                            <div class="mb-3">
                                                <label class="form-label">Experience Required</label>
                                                <select class="form-select" id="jobExperience" required>
                                                    <option value="">Select Experience</option>
                                                    <option value="0-1">0-1 years (Fresher)</option>
                                                    <option value="1-3">1-3 years (Junior)</option>
                                                    <option value="3-5">3-5 years (Mid-level)</option>
                                                    <option value="5-8">5-8 years (Senior)</option>
                                                    <option value="8+">8+ years (Lead/Principal)</option>
                                                </select>
                                            </div>
                                        </div>
                                        <div class="col-md-4">
                                            <div class="mb-3">
                                                <label class="form-label">Job Type</label>
                                                <select class="form-select" id="jobType" required>
                                                    <option value="">Select Type</option>
                                                    <option value="Full-time">Full-time</option>
                                                    <option value="Part-time">Part-time</option>
                                                    <option value="Contract">Contract</option>
                                                    <option value="Internship">Internship</option>
                                                </select>
                                            </div>
                                        </div>
                                        <div class="col-md-4">
                                            <div class="mb-3">
                                                <label class="form-label">Location</label>
                                                <input type="text" class="form-control" id="jobLocation" placeholder="Chennai, Tamil Nadu" required>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="mb-3">
                                        <label class="form-label">Required Skills</label>
                                        <textarea class="form-control" id="jobSkills" rows="2" placeholder="JavaScript, React, Node.js, MongoDB, AWS" required></textarea>
                                        <div class="form-text">Enter skills separated by commas</div>
                                    </div>

                                    <div class="mb-3">
                                        <label class="form-label">Job Description</label>
                                        <textarea class="form-control" id="jobDescription" rows="8" required placeholder="Enter detailed job description including responsibilities, requirements, and qualifications..."></textarea>
                                    </div>

                                    <div class="mb-3">
                                        <label class="form-label">AI Keywords (for better matching)</label>
                                        <textarea class="form-control" id="jobATSKeywords" rows="2" placeholder="software engineer, full stack, web development, agile, scrum"></textarea>
                                        <div class="form-text">Keywords that AI should prioritize when scoring candidates</div>
                                    </div>

                                    <button type="submit" class="btn btn-warning w-100">
                                        <i class="bi bi-plus-circle me-2"></i>Create Job Role
                                    </button>
                                </form>
                            </div>
                        </div>
                    </div>

                    <div class="col-md-4">
                        <div class="card feature-card">
                            <div class="card-header bg-secondary text-white">
                                <h5 class="mb-0">
                                    <i class="bi bi-list-ul me-2"></i>
                                    Existing Jobs
                                </h5>
                            </div>
                            <div class="card-body">
                                <div id="existingJobsList">
                                    <!-- Existing jobs will be populated here -->
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- AI Analysis Tab -->
            <div class="tab-pane fade" id="ai-analysis" role="tabpanel">
                <div class="row">
                    <!-- Job Description Section -->
                    <div class="col-md-6 mb-4">
                        <div class="card feature-card">
                            <div class="card-header bg-info text-white">
                                <h5 class="mb-0">
                                    <i class="bi bi-file-text me-2"></i>
                                    Job Description
                                </h5>
                            </div>
                            <div class="card-body">
                                <div class="mb-3">
                                    <label class="form-label">Select Job Role</label>
                                    <select class="form-select" id="analysisJobSelect">
                                        <option value="">Choose a job role for analysis...</option>
                                    </select>
                                </div>

                                <div id="jobDescriptionDisplay" class="border rounded p-3 bg-light" style="min-height: 200px;">
                                    <div class="text-muted text-center">
                                        <i class="bi bi-briefcase display-4"></i>
                                        <p class="mt-2">Select a job role to view description</p>
                                    </div>
                                </div>

                                <div class="mt-3">
                                    <label class="form-label">Analysis Options</label>
                                    <div class="d-flex gap-2">
                                        <button class="btn btn-danger" id="analyzeAllBtn">
                                            <i class="bi bi-lightning me-2"></i>Analyze All CVs
                                        </button>
                                        <button class="btn btn-outline-danger" id="analyzeSelectedBtn" disabled>
                                            <i class="bi bi-check-square me-2"></i>Analyze Selected
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- AI Chatbot Section -->
                    <div class="col-md-6 mb-4">
                        <div class="card feature-card">
                            <div class="card-header bg-success text-white">
                                <h5 class="mb-0">
                                    <i class="bi bi-robot me-2"></i>
                                    AI Resume Analyzer Chatbot
                                </h5>
                            </div>
                            <div class="card-body">
                                <div id="chatMessages" class="border rounded p-3 mb-3" style="height: 300px; overflow-y: auto; background-color: #f8f9fa;">
                                    <div class="chat-message bot-message mb-2">
                                        <div class="d-flex align-items-start">
                                            <div class="bg-success text-white rounded-circle p-2 me-2" style="width: 32px; height: 32px; display: flex; align-items: center; justify-content: center;">
                                                <i class="bi bi-robot" style="font-size: 14px;"></i>
                                            </div>
                                            <div class="bg-white rounded p-2 shadow-sm">
                                                <small class="text-muted">AI Assistant</small>
                                                <p class="mb-0">Hello! I'm your AI Resume Analyzer. I can help you analyze resumes, provide insights, and answer questions about candidates. How can I assist you today?</p>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <div class="input-group">
                                    <input type="text" class="form-control" id="chatInput" placeholder="Ask me about resume analysis, candidate insights, or job matching...">
                                    <button class="btn btn-success" id="sendChatBtn">
                                        <i class="bi bi-send"></i>
                                    </button>
                                </div>

                                <div class="mt-2">
                                    <small class="text-muted">
                                        <i class="bi bi-info-circle me-1"></i>
                                        AI analyzes resumes in real-time during database updates
                                    </small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Analysis Results Section -->
                <div class="row">
                    <div class="col-12">
                        <div class="card feature-card">
                            <div class="card-header bg-danger text-white">
                                <h5 class="mb-0">
                                    <i class="bi bi-cpu me-2"></i>
                                    AI Analysis Results
                                </h5>
                            </div>
                            <div class="card-body">
                                <div id="analysisProgress" class="mb-4" style="display: none;">
                                    <div class="progress">
                                        <div id="analysisProgressBar" class="progress-bar progress-bar-striped progress-bar-animated bg-danger" role="progressbar"></div>
                                    </div>
                                    <div id="analysisStatus" class="mt-2 text-center"></div>
                                </div>

                                <div id="atsResults">
                                    <!-- AI results will be populated here -->
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- AI Reports Tab -->
            <div class="tab-pane fade" id="ai-reports" role="tabpanel">
                <div class="row">
                    <div class="col-12">
                        <div class="card feature-card">
                            <div class="card-header bg-dark text-white">
                                <h5 class="mb-0">
                                    <i class="bi bi-graph-up me-2"></i>
                                    HR Analytics & Reports
                                </h5>
                            </div>
                            <div class="card-body">
                                <div class="row mb-4">
                                    <div class="col-md-3">
                                        <div class="card text-center border-primary">
                                            <div class="card-body">
                                                <h3 class="text-primary" id="totalCandidatesCount">0</h3>
                                                <p class="mb-0">Total Candidates</p>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="card text-center border-success">
                                            <div class="card-body">
                                                <h3 class="text-success" id="qualifiedCandidatesCount">0</h3>
                                                <p class="mb-0">Qualified (>80%)</p>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="card text-center border-warning">
                                            <div class="card-body">
                                                <h3 class="text-warning" id="averageATSScore">0</h3>
                                                <p class="mb-0">Avg AI Score</p>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="card text-center border-info">
                                            <div class="card-body">
                                                <h3 class="text-info" id="activeJobsCount">0</h3>
                                                <p class="mb-0">Active Jobs</p>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <div class="row">
                                    <div class="col-md-6">
                                        <button class="btn btn-primary w-100 mb-3" id="generateDetailedReport">
                                            <i class="bi bi-file-earmark-pdf me-2"></i>Generate Detailed Report
                                        </button>
                                    </div>
                                    <div class="col-md-6">
                                        <button class="btn btn-success w-100 mb-3" id="exportCandidateData">
                                            <i class="bi bi-file-earmark-excel me-2"></i>Export Candidate Data
                                        </button>
                                    </div>
                                </div>

                                <div id="hrReportsContent">
                                    <!-- Reports content will be populated here -->
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
    <script src="demo-data.js"></script>
    <script src="database-service.js"></script>
    <script src="hr-dashboard.js"></script>
</body>
</html>
